# POS系统核心设计详解

## 1. 自动更新系统设计

### 1.1 更新架构图

```
更新服务器
    ↓
版本检查API → 灰度发布控制
    ↓              ↓
客户端版本检查 → 增量包下载
    ↓              ↓
本地版本对比 → 差分更新应用
    ↓              ↓
自动重启 → 更新完成验证
```

### 1.2 增量更新机制

#### 1.2.1 版本管理策略

```csharp
// 版本信息模型
public class VersionInfo
{
    public string Version { get; set; }          // 版本号 (x.y.z)
    public DateTime ReleaseTime { get; set; }    // 发布时间
    public string Description { get; set; }     // 更新说明
    public bool IsForceUpdate { get; set; }     // 是否强制更新
    public List<string> TargetVersions { get; set; } // 目标版本列表
    public long TotalSize { get; set; }         // 总大小
    public string DownloadUrl { get; set; }     // 下载地址
    public string Hash { get; set; }            // 文件哈希
    public GrayReleaseConfig GrayConfig { get; set; } // 灰度配置
}

// 灰度发布配置
public class GrayReleaseConfig
{
    public bool IsGrayRelease { get; set; }     // 是否灰度发布
    public int GrayPercentage { get; set; }     // 灰度百分比
    public List<string> WhiteListIds { get; set; } // 白名单设备ID
    public List<string> BlackListIds { get; set; } // 黑名单设备ID
    public DateTime StartTime { get; set; }     // 灰度开始时间
    public DateTime EndTime { get; set; }       // 灰度结束时间
}
```

#### 1.2.2 增量更新服务

```csharp
public class UpdateService : IUpdateService
{
    private readonly ILogger<UpdateService> logger;
    private readonly HttpClient httpClient;
    private readonly string currentVersion;
    private readonly string deviceId;
    
    public UpdateService(ILogger<UpdateService> logger, HttpClient httpClient)
    {
        this.logger = logger;
        this.httpClient = httpClient;
        this.currentVersion = GetCurrentVersion();
        this.deviceId = GetDeviceId();
    }
    
    // 检查更新
    public async Task<UpdateCheckResult> CheckForUpdatesAsync()
    {
        try
        {
            var request = new UpdateCheckRequest
            {
                CurrentVersion = currentVersion,
                DeviceId = deviceId,
                Platform = "Windows",
                Architecture = Environment.Is64BitOperatingSystem ? "x64" : "x86"
            };
            
            var response = await httpClient.PostAsJsonAsync("/api/updates/check", request);
            var result = await response.Content.ReadFromJsonAsync<UpdateCheckResult>();
            
            // 检查灰度发布条件
            if (result.HasUpdate && result.VersionInfo.GrayConfig?.IsGrayRelease == true)
            {
                if (!ShouldReceiveGrayUpdate(result.VersionInfo.GrayConfig))
                {
                    result.HasUpdate = false;
                    logger.LogInformation("灰度发布条件不满足，跳过更新");
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "检查更新失败");
            return new UpdateCheckResult { HasUpdate = false, Error = ex.Message };
        }
    }
    
    // 下载更新包
    public async Task<bool> DownloadUpdateAsync(VersionInfo versionInfo, 
        IProgress<DownloadProgress> progress = null)
    {
        try
        {
            var updatePackagePath = Path.Combine(GetTempDirectory(), $"update_{versionInfo.Version}.zip");
            
            using var response = await httpClient.GetAsync(versionInfo.DownloadUrl, HttpCompletionOption.ResponseHeadersRead);
            response.EnsureSuccessStatusCode();
            
            var totalBytes = response.Content.Headers.ContentLength ?? 0;
            var downloadedBytes = 0L;
            
            using var contentStream = await response.Content.ReadAsStreamAsync();
            using var fileStream = new FileStream(updatePackagePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);
            
            var buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
            {
                await fileStream.WriteAsync(buffer, 0, bytesRead);
                downloadedBytes += bytesRead;
                
                // 报告进度
                progress?.Report(new DownloadProgress
                {
                    TotalBytes = totalBytes,
                    DownloadedBytes = downloadedBytes,
                    Percentage = totalBytes > 0 ? (int)((downloadedBytes * 100) / totalBytes) : 0
                });
            }
            
            // 验证文件哈希
            var downloadedHash = CalculateFileHash(updatePackagePath);
            if (downloadedHash != versionInfo.Hash)
            {
                File.Delete(updatePackagePath);
                throw new Exception("更新包校验失败");
            }
            
            logger.LogInformation($"更新包下载完成: {updatePackagePath}");
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "下载更新包失败");
            return false;
        }
    }
    
    // 应用更新
    public async Task<bool> ApplyUpdateAsync(VersionInfo versionInfo)
    {
        try
        {
            var updatePackagePath = Path.Combine(GetTempDirectory(), $"update_{versionInfo.Version}.zip");
            var backupDirectory = CreateBackupDirectory();
            var applicationDirectory = AppDomain.CurrentDomain.BaseDirectory;
            
            // 1. 备份当前版本
            logger.LogInformation("开始备份当前版本");
            await BackupCurrentVersion(applicationDirectory, backupDirectory);
            
            // 2. 解压更新包
            logger.LogInformation("解压更新包");
            ZipFile.ExtractToDirectory(updatePackagePath, GetTempDirectory());
            
            // 3. 应用增量更新
            logger.LogInformation("应用增量更新");
            await ApplyIncrementalUpdate(GetTempDirectory(), applicationDirectory);
            
            // 4. 更新版本信息
            UpdateVersionInfo(versionInfo.Version);
            
            // 5. 清理临时文件
            CleanupTempFiles();
            
            logger.LogInformation($"更新应用成功，版本：{versionInfo.Version}");
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "应用更新失败");
            
            // 回滚到备份版本
            await RollbackToBackup();
            return false;
        }
    }
    
    // 灰度发布条件检查
    private bool ShouldReceiveGrayUpdate(GrayReleaseConfig grayConfig)
    {
        // 检查时间范围
        var now = DateTime.UtcNow;
        if (now < grayConfig.StartTime || now > grayConfig.EndTime)
        {
            return false;
        }
        
        // 检查白名单
        if (grayConfig.WhiteListIds?.Contains(deviceId) == true)
        {
            return true;
        }
        
        // 检查黑名单
        if (grayConfig.BlackListIds?.Contains(deviceId) == true)
        {
            return false;
        }
        
        // 检查灰度百分比
        var hash = deviceId.GetHashCode();
        var percentage = Math.Abs(hash % 100);
        return percentage < grayConfig.GrayPercentage;
    }
}
```

### 1.3 更新UI设计

```typescript
// Vue组件：更新管理器
export class UpdateManager {
  private updateService: UpdateService
  private isChecking = ref(false)
  private downloadProgress = ref(0)
  private updateInfo = ref<VersionInfo | null>(null)
  
  async checkForUpdates(showMessage = true): Promise<void> {
    this.isChecking.value = true
    
    try {
      const result = await this.updateService.checkForUpdates()
      
      if (result.hasUpdate) {
        this.updateInfo.value = result.versionInfo
        this.showUpdateDialog()
      } else if (showMessage) {
        this.showMessage('当前已是最新版本', 'success')
      }
    } catch (error) {
      if (showMessage) {
        this.showMessage('检查更新失败', 'error')
      }
    } finally {
      this.isChecking.value = false
    }
  }
  
  private showUpdateDialog(): void {
    const dialog = DialogPlugin.confirm({
      header: '发现新版本',
      body: `
        <div>
          <p>版本：${this.updateInfo.value?.version}</p>
          <p>大小：${this.formatFileSize(this.updateInfo.value?.totalSize)}</p>
          <p>更新内容：</p>
          <div>${this.updateInfo.value?.description}</div>
        </div>
      `,
      confirmBtn: this.updateInfo.value?.isForceUpdate ? '立即更新' : '现在更新',
      cancelBtn: this.updateInfo.value?.isForceUpdate ? '' : '稍后更新',
      onConfirm: () => {
        this.startUpdate()
        dialog.destroy()
      }
    })
  }
  
  private async startUpdate(): Promise<void> {
    try {
      // 显示下载进度
      const progressDialog = this.showProgressDialog()
      
      // 下载更新包
      const downloadSuccess = await this.updateService.downloadUpdate(
        this.updateInfo.value!,
        (progress) => {
          this.downloadProgress.value = progress.percentage
        }
      )
      
      if (!downloadSuccess) {
        throw new Error('下载失败')
      }
      
      // 应用更新
      progressDialog.update({ body: '正在应用更新...' })
      const applySuccess = await this.updateService.applyUpdate(this.updateInfo.value!)
      
      if (applySuccess) {
        // 重启应用
        await this.restartApplication()
      }
      
    } catch (error) {
      this.showMessage('更新失败：' + error.message, 'error')
    }
  }
}
```

## 2. 日志系统设计

### 2.1 日志分类和结构

#### 2.1.1 日志类型定义

```csharp
public enum LogType
{
    System,      // 系统日志：启动、关闭、配置变更
    Business,    // 业务日志：交易记录、商品操作
    Hardware,    // 硬件日志：设备连接、状态变化
    Error,       // 错误日志：异常信息、错误堆栈
    Performance, // 性能日志：响应时间、资源使用
    Security,    // 安全日志：登录、权限验证
    Audit        // 审计日志：关键操作记录
}

public enum LogLevel
{
    Trace = 0,
    Debug = 1,
    Information = 2,
    Warning = 3,
    Error = 4,
    Critical = 5
}
```

#### 2.1.2 日志模型设计

```csharp
public class LogEntry
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public LogLevel Level { get; set; }
    public LogType Type { get; set; }
    public string Source { get; set; }          // 日志来源
    public string Message { get; set; }         // 日志消息
    public string Details { get; set; }         // 详细信息
    public string UserId { get; set; }          // 操作用户
    public string SessionId { get; set; }       // 会话ID
    public string DeviceId { get; set; }        // 设备ID
    public string Version { get; set; }         // 应用版本
    public Dictionary<string, object> Properties { get; set; } // 扩展属性
    public string StackTrace { get; set; }      // 错误堆栈
    public long ExecutionTime { get; set; }     // 执行时间(ms)
}

// 业务日志扩展
public class BusinessLogEntry : LogEntry
{
    public string OrderId { get; set; }         // 订单ID
    public string ProductId { get; set; }       // 商品ID
    public string PaymentMethod { get; set; }   // 支付方式
    public decimal Amount { get; set; }         // 金额
    public string Operation { get; set; }       // 操作类型
}

// 硬件日志扩展
public class HardwareLogEntry : LogEntry
{
    public string DeviceType { get; set; }      // 设备类型
    public string DeviceName { get; set; }      // 设备名称
    public string Status { get; set; }          // 设备状态
    public string Command { get; set; }         // 执行命令
    public string Response { get; set; }        // 设备响应
}
```

### 2.2 日志收集和存储

#### 2.2.1 本地日志收集

```csharp
public class LoggingService : ILoggingService
{
    private readonly ILogger<LoggingService> logger;
    private readonly LoggingConfiguration config;
    private readonly ConcurrentQueue<LogEntry> logQueue;
    private readonly Timer logProcessTimer;
    private readonly SemaphoreSlim logSemaphore;
    
    public LoggingService(ILogger<LoggingService> logger, LoggingConfiguration config)
    {
        this.logger = logger;
        this.config = config;
        this.logQueue = new ConcurrentQueue<LogEntry>();
        this.logSemaphore = new SemaphoreSlim(1, 1);
        
        // 启动日志处理定时器
        this.logProcessTimer = new Timer(ProcessLogQueue, null, 
            TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(5));
    }
    
    public async Task LogAsync(LogEntry logEntry)
    {
        // 添加系统信息
        logEntry.DeviceId = GetDeviceId();
        logEntry.Version = GetApplicationVersion();
        logEntry.SessionId = GetCurrentSessionId();
        
        // 过滤敏感信息
        FilterSensitiveData(logEntry);
        
        // 添加到队列
        logQueue.Enqueue(logEntry);
        
        // 如果是错误日志，立即处理
        if (logEntry.Level >= LogLevel.Error)
        {
            await ProcessLogQueue(null);
        }
    }
    
    public async Task LogBusinessAsync(string operation, object data, string userId = null)
    {
        var logEntry = new BusinessLogEntry
        {
            Type = LogType.Business,
            Level = LogLevel.Information,
            Source = "Business",
            Message = $"业务操作：{operation}",
            Operation = operation,
            UserId = userId,
            Details = JsonSerializer.Serialize(data)
        };
        
        await LogAsync(logEntry);
    }
    
    public async Task LogHardwareAsync(string deviceType, string operation, 
        string status, Exception exception = null)
    {
        var logEntry = new HardwareLogEntry
        {
            Type = LogType.Hardware,
            Level = exception != null ? LogLevel.Error : LogLevel.Information,
            Source = "Hardware",
            Message = $"硬件操作：{deviceType} - {operation}",
            DeviceType = deviceType,
            Status = status,
            Command = operation,
            StackTrace = exception?.ToString()
        };
        
        await LogAsync(logEntry);
    }
    
    private async void ProcessLogQueue(object state)
    {
        if (!await logSemaphore.WaitAsync(100))
            return;
        
        try
        {
            var logsToProcess = new List<LogEntry>();
            
            // 批量取出日志
            while (logQueue.TryDequeue(out var logEntry) && logsToProcess.Count < 100)
            {
                logsToProcess.Add(logEntry);
            }
            
            if (logsToProcess.Count == 0)
                return;
            
            // 写入本地文件
            await WriteToLocalFile(logsToProcess);
            
            // 上传到远程服务器（如果需要）
            if (ShouldUploadLogs(logsToProcess))
            {
                await UploadLogsAsync(logsToProcess);
            }
        }
        finally
        {
            logSemaphore.Release();
        }
    }
    
    private async Task WriteToLocalFile(List<LogEntry> logs)
    {
        var logDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "POSSystem", "Logs");
        
        Directory.CreateDirectory(logDirectory);
        
        var logFile = Path.Combine(logDirectory, $"pos_{DateTime.Now:yyyyMMdd}.log");
        
        var logLines = logs.Select(log => JsonSerializer.Serialize(log));
        await File.AppendAllLinesAsync(logFile, logLines);
    }
    
    private bool ShouldUploadLogs(List<LogEntry> logs)
    {
        // 包含错误日志立即上传
        if (logs.Any(l => l.Level >= LogLevel.Error))
            return true;
        
        // 根据配置决定是否上传
        return config.EnableRemoteLogging && 
               DateTime.UtcNow.Hour % config.UploadInterval == 0;
    }
}
```

### 2.3 日志上报策略

#### 2.3.1 分布式上报机制

```csharp
public class LogUploadService : ILogUploadService
{
    private readonly HttpClient httpClient;
    private readonly ILogger<LogUploadService> logger;
    private readonly LogUploadConfiguration config;
    
    public async Task<bool> UploadLogsAsync(List<LogEntry> logs)
    {
        try
        {
            // 实现分批上传，避免大量并发
            var batches = logs.Chunk(config.BatchSize);
            var delay = CalculateUploadDelay();
            
            foreach (var batch in batches)
            {
                await Task.Delay(delay); // 错峰上传
                await UploadBatchAsync(batch.ToList());
            }
            
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "日志上传失败");
            return false;
        }
    }
    
    private TimeSpan CalculateUploadDelay()
    {
        // 基于设备ID计算延迟，实现错峰上传
        var deviceHash = GetDeviceId().GetHashCode();
        var delaySeconds = Math.Abs(deviceHash % 300); // 0-5分钟随机延迟
        return TimeSpan.FromSeconds(delaySeconds);
    }
    
    private async Task UploadBatchAsync(List<LogEntry> batch)
    {
        var request = new LogUploadRequest
        {
            DeviceId = GetDeviceId(),
            Timestamp = DateTime.UtcNow,
            Logs = batch
        };
        
        var response = await httpClient.PostAsJsonAsync("/api/logs/upload", request);
        response.EnsureSuccessStatusCode();
    }
}
```

## 3. 响应式布局方案

### 3.1 多分辨率适配策略

#### 3.1.1 CSS变量动态缩放

```scss
// styles/responsive.scss
:root {
  // 基准分辨率：1920x1080
  --base-width: 1920;
  --base-height: 1080;
  
  // 计算缩放比例
  --scale-x: calc(100vw / var(--base-width));
  --scale-y: calc(100vh / var(--base-height));
  --scale: min(var(--scale-x), var(--scale-y));
  
  // 基础尺寸
  --base-font-size: 16px;
  --base-spacing: 20px;
  --base-button-height: 48px;
  --base-input-height: 40px;
  
  // 缩放后的尺寸
  --font-size: calc(var(--base-font-size) * var(--scale));
  --spacing: calc(var(--base-spacing) * var(--scale));
  --button-height: calc(var(--base-button-height) * var(--scale));
  --input-height: calc(var(--base-input-height) * var(--scale));
  
  // 断点定义
  --breakpoint-sm: 1024px;
  --breakpoint-md: 1366px;
  --breakpoint-lg: 1920px;
}

// 应用缩放
.responsive-container {
  font-size: var(--font-size);
  
  .button {
    height: var(--button-height);
    padding: calc(var(--spacing) / 2) var(--spacing);
    border-radius: calc(var(--spacing) / 4);
  }
  
  .input {
    height: var(--input-height);
    padding: 0 calc(var(--spacing) / 2);
  }
  
  .spacing {
    margin: var(--spacing);
    padding: var(--spacing);
  }
}

// 分辨率特定样式
@media (max-width: 1024px) {
  :root {
    --scale: 0.75;
  }
  
  .cashier-layout {
    flex-direction: column;
    
    .product-section {
      height: 50vh;
    }
  }
}

@media (min-width: 1025px) and (max-width: 1366px) {
  :root {
    --scale: 0.85;
  }
}

@media (min-width: 1367px) and (max-width: 1920px) {
  :root {
    --scale: 0.95;
  }
}

@media (min-width: 1921px) {
  :root {
    --scale: 1.1;
  }
}
```

#### 3.1.2 Vue组合式函数：屏幕适配

```typescript
// composables/useResponsive.ts
import { ref, onMounted, onUnmounted } from 'vue'

export function useResponsive() {
  const screenWidth = ref(window.innerWidth)
  const screenHeight = ref(window.innerHeight)
  const scale = ref(1)
  
  // 屏幕类型
  const screenType = computed(() => {
    if (screenWidth.value <= 1024) return 'small'
    if (screenWidth.value <= 1366) return 'medium'
    if (screenWidth.value <= 1920) return 'large'
    return 'xlarge'
  })
  
  // 是否为竖屏
  const isPortrait = computed(() => screenHeight.value > screenWidth.value)
  
  // 计算缩放比例
  const calculateScale = () => {
    const baseWidth = 1920
    const baseHeight = 1080
    
    const scaleX = screenWidth.value / baseWidth
    const scaleY = screenHeight.value / baseHeight
    
    scale.value = Math.min(scaleX, scaleY)
    
    // 设置CSS变量
    document.documentElement.style.setProperty('--screen-scale', scale.value.toString())
  }
  
  // 响应窗口大小变化
  const handleResize = () => {
    screenWidth.value = window.innerWidth
    screenHeight.value = window.innerHeight
    calculateScale()
  }
  
  // 获取响应式尺寸
  const getResponsiveSize = (baseSize: number): number => {
    return Math.round(baseSize * scale.value)
  }
  
  // 获取响应式样式
  const getResponsiveStyle = (styles: Record<string, number>) => {
    const result: Record<string, string> = {}
    
    for (const [key, value] of Object.entries(styles)) {
      result[key] = `${getResponsiveSize(value)}px`
    }
    
    return result
  }
  
  onMounted(() => {
    calculateScale()
    window.addEventListener('resize', handleResize)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
  
  return {
    screenWidth,
    screenHeight,
    screenType,
    isPortrait,
    scale,
    getResponsiveSize,
    getResponsiveStyle
  }
}
```

### 3.2 局部滚动设计

#### 3.2.1 布局结构

```vue
<!-- 收银页面布局 -->
<template>
  <div class="cashier-layout">
    <!-- 固定头部 -->
    <header class="fixed-header">
      <div class="toolbar">
        <!-- 工具栏内容 -->
      </div>
    </header>
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 左侧：商品展示区（可滚动） -->
      <section class="product-section">
        <div class="section-header">
          <h3>商品列表</h3>
          <div class="search-bar">
            <!-- 搜索框 -->
          </div>
        </div>
        
        <div class="scrollable-content">
          <div class="product-grid">
            <!-- 商品网格 -->
          </div>
        </div>
      </section>
      
      <!-- 右侧：购物车和支付区 -->
      <aside class="cart-section">
        <!-- 购物车区域（可滚动） -->
        <div class="cart-container">
          <div class="section-header">
            <h3>购物车</h3>
          </div>
          
          <div class="scrollable-content">
            <div class="cart-items">
              <!-- 购物车商品列表 -->
            </div>
          </div>
        </div>
        
        <!-- 固定支付区域 -->
        <div class="payment-container">
          <div class="total-section">
            <!-- 总计信息 -->
          </div>
          
          <div class="payment-buttons">
            <!-- 支付按钮 -->
          </div>
        </div>
      </aside>
    </main>
    
    <!-- 固定底部（如果需要） -->
    <footer class="fixed-footer">
      <!-- 状态栏等 -->
    </footer>
  </div>
</template>

<style scoped lang="scss">
.cashier-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止整体滚动
}

.fixed-header {
  flex-shrink: 0;
  height: var(--header-height, 80px);
  background: white;
  border-bottom: 1px solid #e6e6e6;
  z-index: 100;
}

.main-content {
  flex: 1;
  display: flex;
  gap: var(--spacing);
  padding: var(--spacing);
  min-height: 0; // 允许flex子项缩小
}

.product-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  
  .section-header {
    flex-shrink: 0;
    padding: var(--spacing);
    border-bottom: 1px solid #f0f0f0;
  }
  
  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing);
    
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

.cart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.cart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  min-height: 0;
  
  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing);
  }
}

.payment-container {
  flex-shrink: 0;
  background: white;
  border-radius: 8px;
  padding: var(--spacing);
  
  .payment-buttons {
    margin-top: var(--spacing);
    
    .payment-button {
      width: 100%;
      height: var(--button-height);
      margin-bottom: calc(var(--spacing) / 2);
      
      // 确保按钮位置固定
      position: relative;
    }
  }
}

.fixed-footer {
  flex-shrink: 0;
  height: var(--footer-height, 40px);
  background: #f5f5f5;
  border-top: 1px solid #e6e6e6;
}

// 响应式调整
@media (max-width: 1366px) {
  .main-content {
    flex-direction: column;
  }
  
  .product-section {
    flex: none;
    height: 50vh;
  }
  
  .cart-section {
    flex: none;
    height: calc(50vh - var(--spacing));
  }
}
</style>
```

#### 3.2.2 虚拟滚动优化

```typescript
// composables/useVirtualScroll.ts
export function useVirtualScroll<T>(items: Ref<T[]>, itemHeight: number, containerHeight: number) {
  const scrollTop = ref(0)
  const containerRef = ref<HTMLElement>()
  
  // 计算可见范围
  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const end = Math.min(start + visibleCount + 2, items.value.length) // 预渲染2个
    
    return { start, end }
  })
  
  // 可见项目
  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.value.slice(start, end).map((item, index) => ({
      item,
      index: start + index,
      top: (start + index) * itemHeight
    }))
  })
  
  // 总高度
  const totalHeight = computed(() => items.value.length * itemHeight)
  
  // 处理滚动
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }
  
  return {
    containerRef,
    visibleItems,
    totalHeight,
    handleScroll
  }
}
``` 

## 4. 统一JSON-RPC桥接系统设计

### 4.1 桥接架构概述

统一JSON-RPC桥接系统是POS系统中Vue前端与C#后端通信的核心组件，它提供了类型安全、可配置、高性能的双向通信能力。

#### 4.1.1 设计目标

1. **统一API**: 通过动态代理提供一致的调用接口
2. **类型安全**: 完整的TypeScript类型支持
3. **灵活配置**: 支持全局配置和单次调用配置
4. **事件驱动**: 强大的事件监听和通知机制
5. **高性能**: 优化的消息处理和错误恢复

#### 4.1.2 核心组件架构

```
┌─────────────────────────────────────────┐
│           Vue前端应用层                  │
├─────────────────────────────────────────┤
│      统一Native接口 (native.*)          │
├─────────────────────────────────────────┤
│    UnifiedJsonRpcClient (动态代理)       │
├─────────────────────────────────────────┤
│  JSON-RPC 2.0 协议层 (WebView2 通信)     │
├─────────────────────────────────────────┤
│      C# JsonRpcHandler 处理器            │
├─────────────────────────────────────────┤
│        硬件服务层 & 系统服务层            │
└─────────────────────────────────────────┘
```

### 4.2 动态代理实现详解

#### 4.2.1 核心代理类设计

```typescript
class UnifiedJsonRpcClient {
  private pendingPromises = new Map<string, PendingPromise>();
  private listeners = new Map<string, Set<ListenerConfig>>();
  private methodConfigs = new Map<string, MethodConfig>();
  private readonly DEFAULT_TIMEOUT = 30000;
  
  constructor() {
    this.initializeMessageListener();
  }
  
  /**
   * 智能参数识别和配置合并
   */
  private async executeMethod(method: string, params: any[], callConfig?: MethodConfig): Promise<any> {
    // 合并全局配置和单次配置（单次配置优先）
    const globalConfig = this.methodConfigs.get(method) || {};
    const config = { ...globalConfig, ...callConfig };
    
    const timeout = config.timeout || this.DEFAULT_TIMEOUT;
    
    // 如果配置为不需要返回值，直接发送通知格式
    if (config.requiresReturn === false) {
      const notification = {
        jsonrpc: '2.0',
        method,
        params: params.length === 1 ? params[0] : params,
      };
      
      window.chrome?.webview?.postMessage(JSON.stringify(notification));
      
      // 记录单次调用（用于调试和性能监控）
      if (callConfig) {
        console.log(`🔥 Fire-and-forget call [${method}] with config:`, callConfig);
      }
      
      return Promise.resolve(undefined);
    }
    
    // 正常的RPC调用处理
    return new Promise((resolve, reject) => {
      const id = uuidv4();
      
      const timeoutTimer = window.setTimeout(() => {
        this.pendingPromises.delete(id);
        reject(new Error(`RPC call timeout for method: ${method} (timeout: ${timeout}ms)`));
      }, timeout);
      
      this.pendingPromises.set(id, { resolve, reject, timeoutTimer });
      
      const request = {
        jsonrpc: '2.0',
        id,
        method,
        params: params.length === 1 ? params[0] : params,
      };
      
      // 记录单次调用配置（用于调试和性能监控）
      if (callConfig) {
        console.log(`📞 RPC call [${method}] with config:`, callConfig);
      }
      
      window.chrome?.webview?.postMessage(JSON.stringify(request));
    });
  }
  
  /**
   * 创建智能动态代理
   */
  createProxy(path: string[] = []): any {
    return new Proxy(() => {}, {
      get: (target, prop: string) => {
        // 避免Promise then调用干扰
        if (prop === 'then') return undefined;
        
        // 特殊方法处理
        switch (prop) {
          case 'on':
            return (method: string, handler: (...args: any[]) => void | Promise<void>, options?: { once?: boolean; context?: any }) => {
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              return this.addListener(fullMethod, handler, options);
            };
            
          case 'once':
            return (method: string, handler: (...args: any[]) => void | Promise<void>, context?: any) => {
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              return this.addListener(fullMethod, handler, { once: true, context });
            };
            
          case 'off':
            return (method: string, handler?: (...args: any[]) => void | Promise<void>) => {
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              this.removeListener(fullMethod, handler);
            };
            
          case 'configure':
            return (method: string, config: MethodConfig) => {
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              this.configureMethod(fullMethod, config);
              return this.createProxy(path); // 返回代理以支持链式调用
            };
            
          case 'listeners':
            return (method?: string) => {
              if (!method) {
                return Array.from(this.listeners.keys());
              }
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              const methodListeners = this.listeners.get(fullMethod);
              return methodListeners ? methodListeners.size : 0;
            };
            
          default:
            return this.createProxy([...path, prop]);
        }
      },
      
      apply: (target, thisArg, args) => {
        const method = path.join('.');
        
        // 智能检查最后一个参数是否为配置对象
        let params = args;
        let callConfig: MethodConfig | undefined;
        
        if (args.length > 0) {
          const lastArg = args[args.length - 1];
          
          // 如果最后一个参数是配置对象（包含特定的配置字段）
          if (lastArg && typeof lastArg === 'object' && 
              (lastArg.hasOwnProperty('requiresReturn') || 
               lastArg.hasOwnProperty('timeout') || 
               lastArg.hasOwnProperty('retries'))) {
            callConfig = lastArg;
            params = args.slice(0, -1); // 移除配置参数
          }
        }
        
        return this.executeMethod(method, params, callConfig);
      },
    });
  }
}

// 全局实例
const rpcClient = new UnifiedJsonRpcClient();
export const native = rpcClient.createProxy();
```

### 4.3 配置系统设计

#### 4.3.1 配置优先级机制

系统支持三层配置优先级：

1. **默认配置** (最低优先级)
2. **全局配置** (中等优先级)
3. **单次配置** (最高优先级)

```typescript
// 配置优先级示例
interface MethodConfig {
  requiresReturn?: boolean;  // 是否需要返回值，默认true
  timeout?: number;          // 超时时间，默认30000ms  
  retries?: number;          // 重试次数，默认0
}

// 1. 默认配置（内置）
const DEFAULT_CONFIG = {
  requiresReturn: true,
  timeout: 30000,
  retries: 0
};

// 2. 全局配置（影响所有后续调用）
native.configure('printer.print', { 
  requiresReturn: false, 
  timeout: 5000 
});

// 3. 单次配置（仅影响当前调用，优先级最高）
await native.printer.print(data, { 
  requiresReturn: true,    // 覆盖全局配置
  timeout: 10000,         // 覆盖全局配置
  retries: 2              // 新增配置
});
```

#### 4.3.2 配置使用场景

**场景1: 高频非关键操作**
```typescript
// 全局配置：提示音不需要返回值
native.configure('app.playSound', { requiresReturn: false, timeout: 500 });

// 使用时直接调用
await native.app.playSound('beep');  // 使用全局配置
await native.app.playSound('success'); // 使用全局配置
```

**场景2: 批量操作优化**
```typescript
// 批量更新库存，只有最后一个需要确认
const products = [product1, product2, product3];
for (let i = 0; i < products.length; i++) {
  const isLast = i === products.length - 1;
  
  await native.inventory.updateStock(products[i], {
    requiresReturn: isLast,           // 只有最后一个需要返回值
    timeout: isLast ? 10000 : 3000   // 最后一个给更长时间
  });
}
```

**场景3: 条件性配置**
```typescript
// 根据业务逻辑动态配置
const processPayment = async (amount: number, isVIP: boolean) => {
  const config = isVIP ? 
    { timeout: 60000, retries: 5 } :     // VIP客户更长超时
    { timeout: 30000, retries: 2 };      // 普通客户标准配置
    
  return native.payment.process({ amount }, config);
};
```

### 4.4 事件系统详解

#### 4.4.1 多层级事件监听

```typescript
// 1. 设备级监听（推荐）
const unsubscribe1 = native.scanner.on('barcodeScanned', (params) => {
  console.log('扫码:', params.barcode, '设备:', params.deviceName);
});

// 2. 全局级监听（用于跨模块通信）
const unsubscribe2 = native.on('hardware.scanner.barcodeScanned', (params) => {
  console.log('全局扫码监听:', params);
});

// 3. 一次性监听
native.cashDrawer.once('opened', (params) => {
  console.log('钱箱已打开');
});

// 4. 条件监听器
const conditionalUnsubscribe = native.printer.on('statusChanged', (params) => {
  if (params.isOnline) {
    console.log('打印机恢复在线');
    // 某些条件下自动取消监听
    if (someCondition) {
      conditionalUnsubscribe();
    }
  }
});
```

#### 4.4.2 事件处理最佳实践

```typescript
// Vue组合式函数中的事件处理
export function useHardwareEvents() {
  const unsubscribers: Array<() => void> = [];
  
  // 扫码事件处理
  const onBarcodeScanned = (handler: (params: ScannerEventParams) => void) => {
    const unsubscribe = native.scanner.on('barcodeScanned', handler);
    unsubscribers.push(unsubscribe);
    return unsubscribe;
  };
  
  // 打印机状态监控
  const onPrinterStatusChanged = (handler: (params: PrinterStatusParams) => void) => {
    const unsubscribe = native.printer.on('statusChanged', handler);
    unsubscribers.push(unsubscribe);
    return unsubscribe;
  };
  
  // 自动清理（Vue组件卸载时）
  onUnmounted(() => {
    unsubscribers.forEach(fn => fn());
  });
  
  return {
    onBarcodeScanned,
    onPrinterStatusChanged
  };
}
```

### 4.5 性能优化和监控

#### 4.5.1 调用性能监控

```typescript
// 性能监控装饰器
class PerformanceMonitor {
  private callStats = new Map<string, {
    totalCalls: number;
    totalTime: number;
    errors: number;
    lastCall: number;
  }>();
  
  wrapMethodCall<T>(method: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    
    return fn().then(
      (result) => {
        this.recordCall(method, performance.now() - startTime, false);
        return result;
      },
      (error) => {
        this.recordCall(method, performance.now() - startTime, true);
        throw error;
      }
    );
  }
  
  private recordCall(method: string, duration: number, isError: boolean) {
    const stats = this.callStats.get(method) || {
      totalCalls: 0,
      totalTime: 0,
      errors: 0,
      lastCall: 0
    };
    
    stats.totalCalls++;
    stats.totalTime += duration;
    stats.lastCall = Date.now();
    
    if (isError) {
      stats.errors++;
    }
    
    this.callStats.set(method, stats);
    
    // 性能警告
    const avgTime = stats.totalTime / stats.totalCalls;
    if (avgTime > 5000) { // 平均响应时间超过5秒
      console.warn(`⚠️ 方法 ${method} 平均响应时间过长: ${avgTime.toFixed(2)}ms`);
    }
  }
  
  getStats() {
    const result: any = {};
    this.callStats.forEach((stats, method) => {
      result[method] = {
        ...stats,
        avgTime: stats.totalTime / stats.totalCalls,
        errorRate: stats.errors / stats.totalCalls
      };
    });
    return result;
  }
}
```

#### 4.5.2 错误恢复机制

```typescript
// 智能重试机制
export function useNativeErrorHandling() {
  const callWithRetry = async <T>(
    methodCall: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await methodCall();
      } catch (error) {
        lastError = error as Error;
        
        // 不可重试的错误
        if (error.message.includes('METHOD_NOT_FOUND')) {
          throw error;
        }
        
        if (attempt < maxRetries) {
          console.warn(`Method call failed, retrying in ${delay}ms... (${attempt + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // 指数退避
        }
      }
    }
    
    throw lastError!;
  };
  
  // 安全调用（不抛出异常）
  const safecall = async <T>(
    methodCall: () => Promise<T>,
    defaultValue?: T
  ): Promise<{ success: boolean; data?: T; error?: Error }> => {
    try {
      const data = await methodCall();
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        data: defaultValue
      };
    }
  };
  
  return { callWithRetry, safecall };
}
```

### 4.6 调试和开发工具

#### 4.6.1 开发模式调试

```typescript
// 开发模式增强
if (import.meta.env.DEV) {
  // RPC调用日志
  const originalExecuteMethod = rpcClient.executeMethod.bind(rpcClient);
  rpcClient.executeMethod = function(method: string, params: any[], config?: MethodConfig) {
    console.group(`🔧 RPC Call: ${method}`);
    console.log('参数:', params);
    console.log('配置:', config);
    console.time(`RPC-${method}`);
    
    return originalExecuteMethod(method, params, config)
      .then(result => {
        console.timeEnd(`RPC-${method}`);
        console.log('结果:', result);
        console.groupEnd();
        return result;
      })
      .catch(error => {
        console.timeEnd(`RPC-${method}`);
        console.error('错误:', error);
        console.groupEnd();
        throw error;
      });
  };
  
  // 全局native对象调试
  (window as any).native = native;
  (window as any).nativeDebug = {
    getStats: () => performanceMonitor.getStats(),
    getListeners: () => Array.from(rpcClient.listeners.keys()),
    getConfigs: () => Array.from(rpcClient.methodConfigs.entries())
  };
}
```

#### 4.6.2 配置检查工具

```typescript
// 配置一致性检查器
export class ConfigurationChecker {
  static checkMethodCall(method: string, args: any[]): {
    hasConfig: boolean;
    configValid: boolean;
    errors: string[];
    detectedConfig?: any;
  } {
    if (args.length === 0) {
      return { hasConfig: false, configValid: true, errors: [] };
    }
    
    const lastArg = args[args.length - 1];
    const hasConfig = this.isValidConfig(lastArg);
    
    if (!hasConfig) {
      return { hasConfig: false, configValid: true, errors: [] };
    }
    
    const validation = this.validateConfigValues(lastArg);
    
    return {
      hasConfig: true,
      configValid: validation.valid,
      errors: validation.errors,
      detectedConfig: lastArg
    };
  }
  
  private static isValidConfig(obj: any): boolean {
    if (!obj || typeof obj !== 'object') return false;
    
    const configFields = ['requiresReturn', 'timeout', 'retries'];
    const keys = Object.keys(obj);
    
    return keys.some(key => configFields.includes(key));
  }
  
  private static validateConfigValues(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (config.hasOwnProperty('requiresReturn') && typeof config.requiresReturn !== 'boolean') {
      errors.push('requiresReturn must be a boolean');
    }
    
    if (config.hasOwnProperty('timeout') && (typeof config.timeout !== 'number' || config.timeout <= 0)) {
      errors.push('timeout must be a positive number');
    }
    
    if (config.hasOwnProperty('retries') && (typeof config.retries !== 'number' || config.retries < 0)) {
      errors.push('retries must be a non-negative number');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}
```

这套统一JSON-RPC桥接系统为POS系统提供了强大的通信基础设施，具备出色的类型安全性、配置灵活性和性能监控能力，确保了前后端之间的高效、可靠通信。 