# JSON-RPC 使用说明

## 📋 架构澄清

基于您的反馈，我们明确了 **JSON-RPC 的使用范围**：

### ✅ JSON-RPC 用于：
- **C# 壳与 Vue 之间的硬件通信**
- **系统级功能调用**
- **C# 到 Vue 的所有通知事件**

### ✅ HTTP API 用于：
- **Vue 直接调用 Java 中台**
- **业务数据的 CRUD 操作**
- **用户认证和权限管理**

## 🏗️ 正确的架构图

```
Vue3前端 ── JSON-RPC ──→ C#桥接层 ──→ 硬件设备
    ↓                        ↑
HTTP API调用           JSON-RPC通知
    ↓                        ↑
Java中台系统 ←──────────────┘
    (业务数据处理)
```

## 🔧 实际应用场景

### 场景1：扫码收银流程

```typescript
// ✅ 正确方式：Vue直接调用HTTP API获取商品
const product = await productApi.getByBarcode(barcode)

// ✅ 正确方式：JSON-RPC调用硬件打印
const printResult = await native.printer.printTicket(ticketData)
```

### 场景2：错误处理

```typescript
try {
  // ✅ Vue直接调用HTTP API创建订单
  const order = await orderApi.create(orderData)
  
  // ✅ JSON-RPC调用硬件打印
  await native.printer.printTicket(order)
} catch (error) {
  // 显示错误消息，要求用户重试
  showMessage('操作失败，请检查网络连接后重试', 'error')
}
```

## 🎯 JSON-RPC 接口定义

### 硬件控制接口

```typescript
export interface NativeApi {
  // 打印机控制
  printer: {
    printTicket(data: PrintTicketData): Promise<PrintResult>
    getStatus(): Promise<PrinterStatus>
    cancelJob(jobId: string): Promise<boolean>
  }
  
  // 扫码枪控制
  scanner: {
    getStatus(): Promise<ScannerStatus>
    startScan(): Promise<void>
    stopScan(): Promise<void>
    testConnection(): Promise<boolean>
  }
  
  // 钱箱控制
  cashDrawer: {
    open(): Promise<boolean>
    getStatus(): Promise<DrawerStatus>
  }
  
  // 系统功能
  app: {
    getVersion(): Promise<string>
    restart(): void
    getSystemInfo(): Promise<SystemInfo>
  }
}
```

## 📝 完整的前端调用打印示例

### 示例1：基础打印调用

```typescript
// 基础打印功能
async function printBasicTicket() {
  try {
    const result = await native.printer.printTicket({
      orderId: 'ORDER-001',
      storeName: '示例商店',
      items: [
        { name: '商品A', quantity: 2, price: 10.00, total: 20.00 },
        { name: '商品B', quantity: 1, price: 15.50, total: 15.50 }
      ],
      totalAmount: 35.50,
      paymentMethod: 'CASH',
      cashReceived: 40.00,
      change: 4.50,
      timestamp: new Date().toLocaleString(),
      footer: '谢谢惠顾，欢迎下次光临！'
    })
    
    if (result.success) {
      console.log(`打印成功，任务ID: ${result.jobId}`)
      
      // 可选：打开钱箱
      await native.cashDrawer.open()
      
      return true
    } else {
      console.error('打印失败:', result.error)
      return false
    }
  } catch (error) {
    console.error('打印调用异常:', error)
    return false
  }
}
```

### 示例2：完整收银流程

```vue
<template>
  <div class="cashier">
    <div class="input-section">
      <t-input 
        v-model="barcode" 
        placeholder="扫码或输入商品条码"
        @enter="searchProduct"
        :loading="searching"
      />
    </div>
    
    <div class="cart-section">
      <div v-for="item in cartItems" :key="item.id" class="cart-item">
        {{item.name}} x{{item.quantity}} = ¥{{item.total.toFixed(2)}}
      </div>
      <div class="total">总计: ¥{{totalAmount.toFixed(2)}}</div>
    </div>
    
    <div class="action-section">
      <t-button 
        @click="checkout" 
        :disabled="cartItems.length === 0"
        :loading="processing"
        theme="primary"
        size="large"
      >
        结账
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { native } from '@/bridge'
import { productApi, orderApi } from '@/services/api'

const barcode = ref('')
const cartItems = ref<CartItem[]>([])
const searching = ref(false)
const processing = ref(false)

const totalAmount = computed(() => 
  cartItems.value.reduce((sum, item) => sum + item.total, 0)
)

// 搜索商品（HTTP API）
const searchProduct = async () => {
  if (!barcode.value.trim()) return
  
  searching.value = true
  try {
    // ✅ Vue直接调用HTTP API
    const product = await productApi.getByBarcode(barcode.value)
    
    if (product) {
      addToCart(product)
      showMessage('商品已添加', 'success')
    } else {
      showMessage('商品不存在', 'warning')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    showMessage('网络错误', 'error')
  } finally {
    searching.value = false
    barcode.value = ''
  }
}

// 添加到购物车
const addToCart = (product: Product) => {
  const existing = cartItems.value.find(item => item.productId === product.id)
  
  if (existing) {
    existing.quantity++
    existing.total = existing.quantity * existing.price
  } else {
    cartItems.value.push({
      id: Date.now().toString(),
      productId: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      total: product.price
    })
  }
}

// 结账流程
const checkout = async () => {
  if (cartItems.value.length === 0) return
  
  processing.value = true
  try {
    // 1. ✅ Vue直接调用HTTP API创建订单
    const order = await orderApi.create({
      items: cartItems.value.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        price: item.price
      })),
      totalAmount: totalAmount.value,
      paymentMethod: 'CASH',
      cashReceived: totalAmount.value,
      change: 0
    })
    
    // 2. ✅ JSON-RPC调用硬件打印小票
    const printSuccess = await printOrderTicket(order)
    
    if (printSuccess) {
      cartItems.value = []
      showMessage('结账成功', 'success')
    }
    
     } catch (error) {
     console.error('结账失败:', error)
     showMessage('结账失败: ' + error.message, 'error')
   } finally {
     processing.value = false
   }
 }

// 打印订单小票
const printOrderTicket = async (order: Order): Promise<boolean> => {
  try {
    // ✅ JSON-RPC调用硬件打印
    const result = await native.printer.printTicket({
      orderId: order.id,
      storeName: '示例商店',
      items: order.items.map(item => ({
        name: item.productName,
        quantity: item.quantity,
        price: item.price,
        total: item.quantity * item.price
      })),
      totalAmount: order.totalAmount,
      paymentMethod: order.paymentMethod,
      cashReceived: order.cashReceived,
      change: order.change,
      timestamp: new Date().toLocaleString(),
      footer: '谢谢惠顾，欢迎下次光临！'
    })
    
    if (result.success) {
      // ✅ 打印成功后打开钱箱
      await native.cashDrawer.open()
      return true
    } else {
      showMessage('打印失败: ' + result.error, 'error')
      return false
    }
  } catch (error) {
    console.error('打印异常:', error)
    showMessage('打印异常: ' + error.message, 'error')
    return false
  }
}



// 监听硬件扫码事件
document.addEventListener('hardware.barcodeScanned', async (event: any) => {
  const { barcode: scannedCode, deviceName } = event.detail
  console.log('收到扫码:', scannedCode, '设备:', deviceName)
  barcode.value = scannedCode
  await searchProduct()
})

// 监听扫码错误事件
document.addEventListener('hardware.scannerError', (event: any) => {
  const { error, errorCode } = event.detail
  showMessage(`扫码失败[${errorCode}]: ${error}`, 'error')
})

// 监听扫码枪连接状态变化
document.addEventListener('hardware.scannerConnectionChanged', (event: any) => {
  const { isConnected, deviceName } = event.detail
  if (isConnected) {
    showMessage(`扫码枪已连接: ${deviceName}`, 'success')
  } else {
    showMessage(`扫码枪连接断开: ${deviceName}`, 'warning')
  }
})

// 监听打印机状态变化
document.addEventListener('hardware.printerStatusChanged', (event: any) => {
  const { isOnline, paperStatus, errorMessage } = event.detail
  
  if (!isOnline) {
    showMessage('打印机离线', 'error')
  } else if (paperStatus === 'EMPTY') {
    showMessage('打印机缺纸，请添加纸张', 'warning')
  } else if (paperStatus === 'LOW') {
    showMessage('打印机纸张不足', 'warning')
  }
  
  if (errorMessage) {
    showMessage(`打印机错误: ${errorMessage}`, 'error')
  }
})

// 监听打印任务完成
document.addEventListener('hardware.printJobCompleted', (event: any) => {
  const { orderId, jobId } = event.detail
  showMessage(`订单 ${orderId} 打印完成`, 'success')
})

// 监听打印任务失败
document.addEventListener('hardware.printJobFailed', (event: any) => {
  const { orderId, error } = event.detail
  showMessage(`订单 ${orderId} 打印失败: ${error}`, 'error')
})

// 监听钱箱状态变化
document.addEventListener('hardware.cashDrawerStatusChanged', (event: any) => {
  const { isOpen } = event.detail
  console.log('钱箱状态:', isOpen ? '打开' : '关闭')
})

// 工具函数
const showMessage = (message: string, type: 'success' | 'warning' | 'error') => {
  // 实现消息提示
  console.log(`[${type.toUpperCase()}] ${message}`)
}
</script>

## 🔍 关键要点

### 1. **职责分工明确**
- **JSON-RPC**: 硬件控制
- **HTTP API**: 业务数据 + 用户认证

### 2. **类型安全保证**
```typescript
// ✅ 编译时类型检查
const result: PrintResult = await native.printer.printTicket(data)

// ✅ 自动代码提示
native.scanner.getStatus() // IDE会提示返回类型
```

### 3. **错误处理策略**
```typescript
try {
  // 在线业务处理
  const order = await orderApi.create(data)
  await native.printer.printTicket(order)
} catch (error) {
  // 显示错误信息，引导用户重试
  showMessage('操作失败，请检查网络连接后重试', 'error')
}
```

### 4. **性能优化**
- HTTP API 直连，减少中间层开销
- JSON-RPC 专注硬件，响应更快
- 架构简化，维护成本低

## 📞 总结

这种架构设计的优势：

1. **简化通信路径**: Vue ↔ Java 直连，减少延迟
2. **职责清晰**: C# 专注硬件，Java 专注业务
3. **类型安全**: JSON-RPC 提供完整的 TypeScript 支持
4. **可维护性**: 硬件逻辑与业务逻辑完全分离
5. **扩展性**: 新增业务功能不影响硬件层

现在 JSON-RPC 的使用范围非常明确：**仅用于硬件控制**，而所有业务数据操作都通过 Vue 直接调用 HTTP API 完成。

---

## 🔔 C# 到 Vue 通知系统完整封装

### 📡 JSON-RPC 2.0 通知规范

所有 C# 到 Vue 的通知都采用标准的 JSON-RPC 2.0 通知格式：

```typescript
// 标准通知格式
interface JsonRpcNotification {
  jsonrpc: '2.0';           // 协议版本
  method: string;           // 通知方法名
  params?: any;             // 通知参数
}

// 通知分类规范
enum NotificationCategory {
  HARDWARE = 'hardware',    // 硬件相关通知
  SYSTEM = 'system',        // 系统相关通知
  BUSINESS = 'business',    // 业务相关通知
  ERROR = 'error'           // 错误通知
}

// 通知方法命名规范: {category}.{device/module}.{event}
// 例如: hardware.scanner.connected, system.network.statusChanged
```

### 🎯 C# 端通知服务完整封装

#### 1. 通知管理器基础类

```csharp
// Services/Notifications/INotificationService.cs
public interface INotificationService
{
    Task SendNotificationAsync<T>(string method, T parameters = default, CancellationToken cancellationToken = default);
    Task SendHardwareNotificationAsync<T>(string device, string eventName, T parameters = default);
    Task SendSystemNotificationAsync<T>(string module, string eventName, T parameters = default);
    Task SendBusinessNotificationAsync<T>(string module, string eventName, T parameters = default);
    Task SendErrorNotificationAsync(string source, string error, object details = null);
}

// Services/Notifications/NotificationService.cs
public class NotificationService : INotificationService
{
    private readonly WebView2 _webView;
    private readonly ILogger<NotificationService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    
    public NotificationService(WebView2 webView, ILogger<NotificationService> logger)
    {
        _webView = webView;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
    }
    
    public async Task SendNotificationAsync<T>(string method, T parameters = default, CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new JsonRpcNotification
            {
                JsonRpc = "2.0",
                Method = method,
                Params = parameters
            };
            
            var json = JsonSerializer.Serialize(notification, _jsonOptions);
            _logger.LogDebug("发送通知: {Method}, 参数: {Params}", method, json);
            
            await _webView.CoreWebView2.PostWebMessageAsStringAsync(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送通知失败: {Method}", method);
            throw;
        }
    }
    
    public async Task SendHardwareNotificationAsync<T>(string device, string eventName, T parameters = default)
    {
        var method = $"hardware.{device}.{eventName}";
        await SendNotificationAsync(method, parameters);
    }
    
    public async Task SendSystemNotificationAsync<T>(string module, string eventName, T parameters = default)
    {
        var method = $"system.{module}.{eventName}";
        await SendNotificationAsync(method, parameters);
    }
    
    public async Task SendBusinessNotificationAsync<T>(string module, string eventName, T parameters = default)
    {
        var method = $"business.{module}.{eventName}";
        await SendNotificationAsync(method, parameters);
    }
    
    public async Task SendErrorNotificationAsync(string source, string error, object details = null)
    {
        var parameters = new
        {
            source,
            error,
            details,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await SendNotificationAsync("error.occurred", parameters);
    }
}

// 通知模型定义
public class JsonRpcNotification
{
    [JsonPropertyName("jsonrpc")]
    public string JsonRpc { get; set; } = "2.0";
    
    [JsonPropertyName("method")]
    public string Method { get; set; }
    
    [JsonPropertyName("params")]
    public object Params { get; set; }
}
```

#### 2. 硬件设备通知封装

```csharp
// Services/Hardware/ScannerNotificationService.cs
public class ScannerNotificationService
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<ScannerNotificationService> _logger;
    
    public ScannerNotificationService(INotificationService notificationService, ILogger<ScannerNotificationService> logger)
    {
        _notificationService = notificationService;
        _logger = logger;
    }
    
    // 扫码成功通知
    public async Task NotifyBarcodeScannedAsync(string barcode, string deviceName)
    {
        var parameters = new
        {
            barcode,
            deviceName,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("scanner", "barcodeScanned", parameters);
        _logger.LogInformation("扫码通知已发送: {Barcode}", barcode);
    }
    
    // 扫码错误通知
    public async Task NotifyScannerErrorAsync(string barcode, string error, string errorCode = null)
    {
        var parameters = new
        {
            barcode,
            error,
            errorCode = errorCode ?? "SCAN_FAILED",
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("scanner", "error", parameters);
        _logger.LogWarning("扫码错误通知已发送: {Error}", error);
    }
    
    // 连接状态变化通知
    public async Task NotifyConnectionChangedAsync(bool isConnected, string deviceName)
    {
        var parameters = new
        {
            isConnected,
            deviceName,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("scanner", "connectionChanged", parameters);
        _logger.LogInformation("扫码枪连接状态通知: {IsConnected}", isConnected);
    }
}

// Services/Hardware/PrinterNotificationService.cs
public class PrinterNotificationService
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<PrinterNotificationService> _logger;
    
    public PrinterNotificationService(INotificationService notificationService, ILogger<PrinterNotificationService> logger)
    {
        _notificationService = notificationService;
        _logger = logger;
    }
    
    // 打印机状态变化通知
    public async Task NotifyStatusChangedAsync(bool isOnline, string paperStatus, string errorMessage = null)
    {
        var parameters = new
        {
            isOnline,
            paperStatus, // OK, EMPTY, LOW
            errorMessage,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("printer", "statusChanged", parameters);
        _logger.LogInformation("打印机状态通知: Online={IsOnline}, Paper={PaperStatus}", isOnline, paperStatus);
    }
    
    // 打印任务完成通知
    public async Task NotifyPrintJobCompletedAsync(string jobId, string orderId)
    {
        var parameters = new
        {
            jobId,
            orderId,
            success = true,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("printer", "jobCompleted", parameters);
        _logger.LogInformation("打印完成通知: JobId={JobId}, OrderId={OrderId}", jobId, orderId);
    }
    
    // 打印任务失败通知
    public async Task NotifyPrintJobFailedAsync(string orderId, string error, string errorCode = null)
    {
        var parameters = new
        {
            orderId,
            error,
            errorCode = errorCode ?? "PRINT_FAILED",
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("printer", "jobFailed", parameters);
        _logger.LogError("打印失败通知: OrderId={OrderId}, Error={Error}", orderId, error);
    }
}

// Services/Hardware/CashDrawerNotificationService.cs
public class CashDrawerNotificationService
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<CashDrawerNotificationService> _logger;
    
    public CashDrawerNotificationService(INotificationService notificationService, ILogger<CashDrawerNotificationService> logger)
    {
        _notificationService = notificationService;
        _logger = logger;
    }
    
    // 钱箱打开通知
    public async Task NotifyDrawerOpenedAsync(bool success)
    {
        var parameters = new
        {
            success,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("cashDrawer", "opened", parameters);
        _logger.LogInformation("钱箱打开通知: Success={Success}", success);
    }
    
    // 钱箱状态变化通知
    public async Task NotifyStatusChangedAsync(bool isOpen)
    {
        var parameters = new
        {
            isOpen,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("cashDrawer", "statusChanged", parameters);
        _logger.LogInformation("钱箱状态通知: IsOpen={IsOpen}", isOpen);
    }
    
    // 钱箱错误通知
    public async Task NotifyErrorAsync(string error, string operation)
    {
        var parameters = new
        {
            error,
            operation,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("cashDrawer", "error", parameters);
        _logger.LogError("钱箱错误通知: Operation={Operation}, Error={Error}", operation, error);
    }
}
```

#### 3. 系统通知封装

```csharp
// Services/System/SystemNotificationService.cs
public class SystemNotificationService
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<SystemNotificationService> _logger;
    
    public SystemNotificationService(INotificationService notificationService, ILogger<SystemNotificationService> logger)
    {
        _notificationService = notificationService;
        _logger = logger;
    }
    
    // 网络状态变化通知
    public async Task NotifyNetworkStatusChangedAsync(bool isOnline, string connectionType = null)
    {
        var parameters = new
        {
            isOnline,
            connectionType, // WiFi, Ethernet, Mobile, Unknown
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendSystemNotificationAsync("network", "statusChanged", parameters);
        _logger.LogInformation("网络状态通知: IsOnline={IsOnline}, Type={ConnectionType}", isOnline, connectionType);
    }
    
    // 应用更新通知
    public async Task NotifyUpdateAvailableAsync(string version, bool isForceUpdate, string description)
    {
        var parameters = new
        {
            version,
            isForceUpdate,
            description,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendSystemNotificationAsync("app", "updateAvailable", parameters);
        _logger.LogInformation("更新通知: Version={Version}, Force={IsForceUpdate}", version, isForceUpdate);
    }
    
    // 内存使用警告通知
    public async Task NotifyMemoryWarningAsync(long usedMemory, long totalMemory, double usagePercentage)
    {
        var parameters = new
        {
            usedMemory,
            totalMemory,
            usagePercentage,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendSystemNotificationAsync("memory", "warning", parameters);
        _logger.LogWarning("内存警告通知: Usage={UsagePercentage:F1}%", usagePercentage);
    }
    
    // 配置变更通知
    public async Task NotifyConfigChangedAsync(string configKey, object oldValue, object newValue)
    {
        var parameters = new
        {
            configKey,
            oldValue,
            newValue,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendSystemNotificationAsync("config", "changed", parameters);
        _logger.LogInformation("配置变更通知: Key={ConfigKey}", configKey);
    }
}
```

### 🎯 Vue 端统一桥接封装

基于动态代理的统一桥接API已集成在 `src/bridge/index.ts` 中，提供方法调用和事件监听的一体化接口：

#### 1. 统一API特性

- **方法调用**: 支持异步RPC调用，自动处理超时和错误
- **事件监听**: 设备级和全局级事件监听
- **配置管理**: 可配置方法是否需要返回值、超时时间等
- **类型安全**: 完整的TypeScript类型定义
- **向后兼容**: 保持DOM事件支持
- **自动清理**: 一次性监听器自动移除
- **链式调用**: 支持配置的链式调用

#### 2. 基础使用方式

```typescript
import { native } from '@/bridge'

// 方法调用（默认需要返回值）
const result = await native.printer.printTicket(data)
const status = await native.scanner.getStatus()

// 全局配置方法行为（影响所有后续调用）
native.configure('printer.beep', { requiresReturn: false })
await native.printer.beep() // 使用全局配置：不等待返回值

// 单次调用配置（仅影响当前调用，优先级更高）
await native.printer.beep({}, { requiresReturn: false }) // 单次：不需要返回值
await native.printer.printTicket(data, { timeout: 45000 }) // 单次：自定义超时时间

// 混合使用全局和单次配置
native.configure('scanner.scan', { timeout: 5000 }) // 全局：5秒超时
await native.scanner.scan() // 使用全局配置
await native.scanner.scan({}, { timeout: 10000 }) // 单次覆盖：10秒超时

// 事件监听 - 设备级
const unsubscribe1 = native.scanner.on('barcodeScanned', (params) => {
  console.log('扫码:', params.barcode, '设备:', params.deviceName)
})

const unsubscribe2 = native.printer.on('statusChanged', (params) => {
  console.log('打印机状态:', params.isOnline ? '在线' : '离线')
})

// 事件监听 - 全局级
native.on('hardware.scanner.barcodeScanned', (params) => {
  console.log('全局扫码监听:', params)
})

// 一次性监听
native.cashDrawer.once('opened', (params) => {
  console.log('钱箱已打开')
})

// 移除监听
native.scanner.off('barcodeScanned')
unsubscribe1() // 或使用返回的取消函数
```

#### 2. 高级组合式函数封装

```typescript
// src/composables/useNativeDevice.ts
import { ref, onUnmounted } from 'vue'
import { native } from '@/bridge'

// 扫码器组合式函数
export function useScanner() {
  const isConnected = ref(false)
  const lastBarcode = ref('')
  const deviceName = ref('')
  
  const unsubscribers: Array<() => void> = []
  
  // 监听连接状态
  const unsubscribe1 = native.scanner.on('connectionChanged', (params) => {
    isConnected.value = params.isConnected
    deviceName.value = params.deviceName
  })
  
  // 监听扫码事件
  const unsubscribe2 = native.scanner.on('barcodeScanned', (params) => {
    lastBarcode.value = params.barcode
  })
  
  unsubscribers.push(unsubscribe1, unsubscribe2)
  
  const getStatus = async () => {
    try {
      return await native.scanner.getStatus()
    } catch (error) {
      console.error('获取扫码器状态失败:', error)
      return null
    }
  }
  
  const startScan = async () => {
    try {
      await native.scanner.startScan()
    } catch (error) {
      console.error('启动扫码失败:', error)
    }
  }
  
  const stopScan = async () => {
    try {
      await native.scanner.stopScan()
    } catch (error) {
      console.error('停止扫码失败:', error)
    }
  }
  
  // 自定义事件监听
  const onBarcodeScanned = (handler: (params: any) => void) => {
    const unsubscribe = native.scanner.on('barcodeScanned', handler)
    unsubscribers.push(unsubscribe)
    return unsubscribe
  }
  
  // 组件卸载时自动清理
  onUnmounted(() => {
    unsubscribers.forEach(fn => fn())
  })
  
  return {
    isConnected: readonly(isConnected),
    lastBarcode: readonly(lastBarcode),
    deviceName: readonly(deviceName),
    getStatus,
    startScan,
    stopScan,
    onBarcodeScanned
  }
}

// 打印机组合式函数
export function usePrinter() {
  const isOnline = ref(false)
  const paperStatus = ref<'OK' | 'EMPTY' | 'LOW'>('OK')
  const isPrinting = ref(false)
  
  const unsubscribers: Array<() => void> = []
  
  // 监听状态变化
  const unsubscribe1 = native.printer.on('statusChanged', (params) => {
    isOnline.value = params.isOnline
    paperStatus.value = params.paperStatus
  })
  
  // 监听打印任务
  const unsubscribe2 = native.printer.on('jobCompleted', () => {
    isPrinting.value = false
  })
  
  const unsubscribe3 = native.printer.on('jobFailed', () => {
    isPrinting.value = false
  })
  
  unsubscribers.push(unsubscribe1, unsubscribe2, unsubscribe3)
  
  const printTicket = async (data: any) => {
    try {
      isPrinting.value = true
      const result = await native.printer.printTicket(data)
      return result
    } catch (error) {
      isPrinting.value = false
      throw error
    }
  }
  
  const getStatus = async () => {
    try {
      return await native.printer.getStatus()
    } catch (error) {
      console.error('获取打印机状态失败:', error)
      return null
    }
  }
  
  // 组件卸载时自动清理
  onUnmounted(() => {
    unsubscribers.forEach(fn => fn())
  })
  
  return {
    isOnline: readonly(isOnline),
    paperStatus: readonly(paperStatus),
    isPrinting: readonly(isPrinting),
    printTicket,
    getStatus
  }
}

// 系统事件监听
export function useSystemEvents() {
  const networkStatus = ref({ isOnline: true, connectionType: 'Unknown' })
  const memoryUsage = ref({ used: 0, total: 0, percentage: 0 })
  
  const unsubscribers: Array<() => void> = []
  
  // 监听网络状态
  const unsubscribe1 = native.system.on('networkStatusChanged', (params) => {
    networkStatus.value = {
      isOnline: params.isOnline,
      connectionType: params.connectionType || 'Unknown'
    }
  })
  
  // 监听内存警告
  const unsubscribe2 = native.app.on('memoryWarning', (params) => {
    memoryUsage.value = {
      used: params.usedMemory,
      total: params.totalMemory,
      percentage: params.usagePercentage
    }
  })
  
  unsubscribers.push(unsubscribe1, unsubscribe2)
  
  // 组件卸载时自动清理
  onUnmounted(() => {
    unsubscribers.forEach(fn => fn())
  })
  
  return {
    networkStatus: readonly(networkStatus),
    memoryUsage: readonly(memoryUsage)
  }
}

// 通用事件监听组合式函数
export function useNativeEvents() {
  const unsubscribers: Array<() => void> = []
  
  const on = (method: string, handler: (...args: any[]) => void, options?: { once?: boolean }) => {
    let unsubscribe: () => void
    
    if (options?.once) {
      unsubscribe = native.once(method, handler)
    } else {
      unsubscribe = native.on(method, handler)
    }
    
    unsubscribers.push(unsubscribe)
    return unsubscribe
  }
  
  const off = (method: string, handler?: (...args: any[]) => void) => {
    native.off(method, handler)
  }
  
  // 组件卸载时自动清理
  onUnmounted(() => {
    unsubscribers.forEach(fn => fn())
  })
  
  return { on, off }
}
```

#### 3. 全局配置与单次配置的灵活运用

```typescript
// src/composables/useNativeConfig.ts
import { native } from '@/bridge'

// 配置管理组合式函数
export function useNativeConfig() {
  
  // 配置不需要返回值的方法（全局生效）
  const configureFireAndForget = (methods: string[]) => {
    methods.forEach(method => {
      native.configure(method, { requiresReturn: false })
    })
  }
  
  // 配置批量设置（全局生效）
  const configureBatch = (configs: Record<string, { requiresReturn?: boolean; timeout?: number }>) => {
    Object.entries(configs).forEach(([method, config]) => {
      native.configure(method, config)
    })
  }
  
  // 预设配置方案（全局生效）
  const applyPresetConfig = (preset: 'fast' | 'reliable' | 'minimal') => {
    switch (preset) {
      case 'fast':
        // 快速响应配置
        configureBatch({
          'printer.beep': { requiresReturn: false, timeout: 1000 },
          'scanner.beep': { requiresReturn: false, timeout: 1000 },
          'cashDrawer.open': { timeout: 3000 },
        })
        break
        
      case 'reliable':
        // 可靠性优先配置
        configureBatch({
          'printer.printTicket': { timeout: 60000 },
          'printer.getStatus': { timeout: 10000 },
          'scanner.getStatus': { timeout: 5000 },
        })
        break
        
      case 'minimal':
        // 最小化返回值配置
        configureFireAndForget([
          'printer.beep',
          'scanner.beep',
          'app.log',
          'system.notify'
        ])
        break
    }
  }
  
  // 单次调用配置辅助函数
  const withConfig = (config: { requiresReturn?: boolean; timeout?: number; retries?: number }) => {
    return config
  }
  
  // 批量操作辅助函数
  const batchCall = async <T>(
    items: T[],
    operation: (item: T, index: number, isLast: boolean) => Promise<any>,
    options?: {
      requireReturnOnLast?: boolean,
      batchSize?: number,
      delay?: number
    }
  ) => {
    const { requireReturnOnLast = true, batchSize = 10, delay = 0 } = options || {}
    const results = []
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize)
      
      const batchPromises = batch.map(async (item, batchIndex) => {
        const globalIndex = i + batchIndex
        const isLast = globalIndex === items.length - 1
        
        if (delay > 0 && globalIndex > 0) {
          await new Promise(resolve => setTimeout(resolve, delay))
        }
        
        return operation(item, globalIndex, isLast)
      })
      
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
    }
    
    return results
  }
  
  // 监听器统计
  const getListenerStats = () => {
    const allMethods = native.listeners()
    const stats = allMethods.map(method => ({
      method,
      count: native.listeners(method)
    }))
    
    return {
      totalMethods: allMethods.length,
      totalListeners: stats.reduce((sum, stat) => sum + stat.count, 0),
      details: stats
    }
  }
  
  return {
    configureFireAndForget,
    configureBatch,
    applyPresetConfig,
    withConfig,
    batchCall,
    getListenerStats
  }
}

// 使用示例
export function useAdvancedNativeOperations() {
  const { withConfig, batchCall } = useNativeConfig()
  
  // 单次配置示例
  const quickBeep = () => native.printer.beep({}, withConfig({ requiresReturn: false }))
  
  const reliablePrint = (data: any) => native.printer.printTicket(data, withConfig({ 
    timeout: 60000, 
    retries: 3 
  }))
  
  // 批量操作示例
  const updateMultipleProducts = async (products: Product[]) => {
    return batchCall(
      products,
      async (product, index, isLast) => {
        // 只有最后一个需要返回值确认
        return native.inventory.updateProduct(product, withConfig({
          requiresReturn: isLast,
          timeout: isLast ? 10000 : 3000
        }))
      },
      {
        requireReturnOnLast: true,
        batchSize: 5,
        delay: 100 // 每个操作间隔100ms
      }
    )
  }
  
  // 条件配置示例
  const conditionalPrint = async (data: any, urgent = false) => {
    const config = urgent 
      ? withConfig({ timeout: 30000, retries: 5 })
      : withConfig({ timeout: 60000, retries: 2 })
      
    return native.printer.printTicket(data, config)
  }
  
  return {
    quickBeep,
    reliablePrint,
    updateMultipleProducts,
    conditionalPrint
  }
}

// 错误处理和重试
export function useNativeErrorHandling() {
  
  // 带重试的方法调用
  const callWithRetry = async <T>(
    methodCall: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    let lastError: Error
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await methodCall()
      } catch (error) {
        lastError = error as Error
        
        if (attempt < maxRetries) {
          console.warn(`Method call failed, retrying in ${delay}ms... (${attempt + 1}/${maxRetries})`)
          await new Promise(resolve => setTimeout(resolve, delay))
          delay *= 2 // 指数退避
        }
      }
    }
    
    throw lastError!
  }
  
  // 安全的方法调用（不抛出异常）
  const safecall = async <T>(
    methodCall: () => Promise<T>,
    defaultValue?: T
  ): Promise<{ success: boolean; data?: T; error?: Error }> => {
    try {
      const data = await methodCall()
      return { success: true, data }
    } catch (error) {
      return { 
        success: false, 
        error: error as Error,
        data: defaultValue
      }
    }
  }
  
  return {
    callWithRetry,
    safecall
  }
}

// 性能监控
export function useNativePerformance() {
  const metrics = ref({
    totalCalls: 0,
    totalErrors: 0,
    averageResponseTime: 0,
    lastCallTime: 0
  })
  
  // 包装方法调用以收集性能数据
  const wrapMethodCall = <T>(methodCall: () => Promise<T>) => {
    return async (): Promise<T> => {
      const startTime = performance.now()
      metrics.value.totalCalls++
      
      try {
        const result = await methodCall()
        const responseTime = performance.now() - startTime
        
        // 更新平均响应时间
        metrics.value.averageResponseTime = (
          metrics.value.averageResponseTime * (metrics.value.totalCalls - 1) + responseTime
        ) / metrics.value.totalCalls
        
        metrics.value.lastCallTime = responseTime
        
        return result
      } catch (error) {
        metrics.value.totalErrors++
        throw error
      }
    }
  }
  
  const resetMetrics = () => {
    metrics.value = {
      totalCalls: 0,
      totalErrors: 0,
      averageResponseTime: 0,
      lastCallTime: 0
    }
  }
  
  return {
    metrics: readonly(metrics),
    wrapMethodCall,
    resetMetrics
  }
}
```

### 📝 完整使用示例

#### 1. 现代化收银页面实现

```vue
<!-- CashierView.vue -->
<template>
  <div class="cashier-view">
    <!-- 硬件状态栏 -->
    <div class="status-bar">
      <div class="hardware-status">
        <span :class="['status-dot', scanner.isConnected ? 'online' : 'offline']"></span>
        扫码枪: {{ scanner.isConnected ? `已连接 (${scanner.deviceName})` : '未连接' }}
        
        <span :class="['status-dot', printer.isOnline ? 'online' : 'offline']"></span>
        打印机: {{ printer.isOnline ? '在线' : '离线' }}
        <span v-if="printer.paperStatus === 'LOW'" class="warning">纸张不足</span>
        <span v-if="printer.paperStatus === 'EMPTY'" class="error">缺纸</span>
        
        <span :class="['status-dot', systemEvents.networkStatus.isOnline ? 'online' : 'offline']"></span>
        网络: {{ systemEvents.networkStatus.isOnline ? '在线' : '离线' }}
      </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
      <div class="input-section">
        <t-input 
          v-model="barcodeInput" 
          placeholder="扫码或输入商品条码"
          @enter="handleManualInput"
          :loading="isSearching"
        />
      </div>
      
      <div class="cart-section">
        <h3>购物车 ({{ cartItems.length }}件)</h3>
        <div v-for="item in cartItems" :key="item.id" class="cart-item">
          <span>{{ item.name }} x{{ item.quantity }}</span>
          <span>¥{{ item.total.toFixed(2) }}</span>
        </div>
        <div class="total">总计: ¥{{ totalAmount.toFixed(2) }}</div>
      </div>
      
      <div class="action-section">
        <t-button 
          @click="handleCheckout" 
          :disabled="cartItems.length === 0 || !printer.isOnline"
          :loading="printer.isPrinting"
          theme="primary"
          size="large"
        >
          结账打印
        </t-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { native } from '@/bridge'
import { useScanner, usePrinter, useSystemEvents } from '@/composables/useNativeDevice'
import { useNativeConfig, useNativeErrorHandling } from '@/composables/useNativeConfig'
import { productApi, orderApi } from '@/services/api'

// 使用硬件组合式函数
const scanner = useScanner()
const printer = usePrinter()
const systemEvents = useSystemEvents()

// 配置和错误处理
const { applyPresetConfig } = useNativeConfig()
const { callWithRetry, safecall } = useNativeErrorHandling()

// 业务状态
const barcodeInput = ref('')
const cartItems = ref<CartItem[]>([])
const isSearching = ref(false)

// 计算属性
const totalAmount = computed(() => 
  cartItems.value.reduce((sum, item) => sum + item.total, 0)
)

// 应用预设配置
applyPresetConfig('fast')

// 监听扫码事件
scanner.onBarcodeScanned(async (params) => {
  console.log(`自动扫码: ${params.barcode}`)
  await handleBarcodeScanned(params.barcode)
})

// 监听全局错误
native.on('error.occurred', (params) => {
  console.error(`系统错误 [${params.source}]:`, params.error)
  showMessage(`系统错误: ${params.error}`, 'error')
})

// 处理扫码（自动或手动）
const handleBarcodeScanned = async (barcode: string) => {
  if (!barcode.trim()) return
  
  isSearching.value = true
  try {
    // 扫码成功时播放提示音（不需要等待返回）
    native.scanner.beep({}, { requiresReturn: false })
    
    // 使用重试机制查询商品
    const product = await callWithRetry(
      () => productApi.getByBarcode(barcode),
      3, // 最多重试3次
      1000 // 1秒延迟
    )
    
    if (product.success && product.data) {
      addToCart(product.data)
      showMessage('商品已添加', 'success')
      
      // 添加成功时播放不同的提示音（单次配置较短超时）
      native.app.playSound('success', { requiresReturn: false, timeout: 500 })
    } else {
      showMessage('商品不存在', 'warning')
      
      // 错误时播放错误提示音
      native.app.playSound('error', { requiresReturn: false, timeout: 500 })
    }
  } catch (error) {
    console.error('查询商品失败:', error)
    showMessage('查询失败，请重试', 'error')
  } finally {
    isSearching.value = false
  }
}

// 手动输入处理
const handleManualInput = async () => {
  await handleBarcodeScanned(barcodeInput.value)
  barcodeInput.value = ''
}

// 添加到购物车
const addToCart = (product: Product) => {
  const existing = cartItems.value.find(item => item.productId === product.id)
  
  if (existing) {
    existing.quantity++
    existing.total = existing.quantity * existing.price
  } else {
    cartItems.value.push({
      id: Date.now().toString(),
      productId: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      total: product.price
    })
  }
}

// 结账流程
const handleCheckout = async () => {
  if (cartItems.value.length === 0) return
  
  try {
    // 结账开始提示音（立即执行，不等待）
    native.app.playSound('checkout-start', { requiresReturn: false })
    
    // 1. 创建订单（HTTP API）
    const orderResult = await safecall(() => orderApi.create({
      items: cartItems.value,
      totalAmount: totalAmount.value,
      paymentMethod: 'CASH',
      cashReceived: totalAmount.value,
      change: 0
    }))
    
    if (!orderResult.success) {
      throw new Error('订单创建失败')
    }
    
    // 2. 打印小票（JSON-RPC，重要操作需要确认返回值和较长超时）
    const printResult = await safecall(() => printer.printTicket({
      orderId: orderResult.data!.id,
      storeName: '示例商店',
      items: cartItems.value,
      totalAmount: totalAmount.value,
      paymentMethod: 'CASH',
      cashReceived: totalAmount.value,
      change: 0,
      timestamp: new Date().toLocaleString(),
      footer: '谢谢惠顾，欢迎下次光临！'
    }, { 
      timeout: 60000,    // 打印可能需要较长时间
      retries: 3,        // 重要操作多重试几次
      requiresReturn: true // 确保获得打印结果
    }))
    
    if (printResult.success) {
      // 3. 清空购物车
      cartItems.value = []
      showMessage('结账成功', 'success')
      
      // 4. 打开钱箱（需要确认是否成功，但超时时间较短）
      const drawerResult = await safecall(() => 
        native.cashDrawer.open({}, { timeout: 5000, requiresReturn: true })
      )
      
      if (!drawerResult.success) {
        console.warn('钱箱打开失败:', drawerResult.error?.message)
      }
      
      // 5. 结账成功提示音（不等待返回）
      native.app.playSound('checkout-success', { requiresReturn: false })
      
      // 6. 更新本地统计（批量操作，只需最后一个返回确认）
      await updateLocalStats([
        { type: 'sale', amount: totalAmount.value },
        { type: 'transaction', count: 1 },
        { type: 'daily-summary', data: orderResult.data }
      ])
      
    } else {
      showMessage('打印失败，请检查打印机', 'error')
      native.app.playSound('error', { requiresReturn: false })
    }
    
  } catch (error) {
    console.error('结账失败:', error)
    showMessage(`结账失败: ${error.message}`, 'error')
    native.app.playSound('error', { requiresReturn: false })
  }
}

// 批量更新本地统计的辅助函数
const updateLocalStats = async (stats: Array<{type: string, amount?: number, count?: number, data?: any}>) => {
  for (let i = 0; i < stats.length; i++) {
    const stat = stats[i]
    const isLast = i === stats.length - 1
    
    // 只有最后一个操作需要返回值确认
    await native.local.updateStat(stat, {
      requiresReturn: isLast,
      timeout: isLast ? 5000 : 1000
    })
  }
}

// 工具函数
const showMessage = (message: string, type: 'success' | 'warning' | 'error') => {
  // 实现消息提示
  console.log(`[${type.toUpperCase()}] ${message}`)
}
</script>

<style scoped>
.status-bar {
  background: #f5f5f5;
  padding: 8px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.status-dot.online { background: #52c41a; }
.status-dot.offline { background: #ff4d4f; }

.warning { color: #fa8c16; font-weight: bold; }
.error { color: #ff4d4f; font-weight: bold; }

.main-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.total {
  font-size: 18px;
  font-weight: bold;
  margin-top: 16px;
  padding: 16px 0;
  border-top: 2px solid #1890ff;
}
</style>
```

#### 2. 系统监控和调试页面

```vue
<!-- SystemMonitor.vue -->
<template>
  <div class="system-monitor">
    <!-- 性能指标 -->
    <div class="metrics-section">
      <h3>性能指标</h3>
      <div class="metrics-grid">
        <div class="metric-card">
          <h4>RPC调用</h4>
          <p>总次数: {{ performance.metrics.totalCalls }}</p>
          <p>错误次数: {{ performance.metrics.totalErrors }}</p>
          <p>成功率: {{ successRate }}%</p>
        </div>
        
        <div class="metric-card">
          <h4>响应时间</h4>
          <p>平均: {{ performance.metrics.averageResponseTime.toFixed(2) }}ms</p>
          <p>最近: {{ performance.metrics.lastCallTime.toFixed(2) }}ms</p>
        </div>
        
        <div class="metric-card">
          <h4>事件监听器</h4>
          <p>活跃方法: {{ listenerStats.totalMethods }}</p>
          <p>监听器总数: {{ listenerStats.totalListeners }}</p>
        </div>
        
        <div class="metric-card">
          <h4>系统状态</h4>
          <p>内存使用: {{ systemEvents.memoryUsage.percentage.toFixed(1) }}%</p>
          <p>网络: {{ systemEvents.networkStatus.isOnline ? '在线' : '离线' }}</p>
        </div>
      </div>
    </div>
    
    <!-- 实时日志 -->
    <div class="log-section">
      <div class="log-header">
        <h3>实时事件日志</h3>
        <div class="log-controls">
          <t-button size="small" @click="clearLogs">清空日志</t-button>
          <t-button size="small" @click="pauseLogging">
            {{ isPaused ? '继续' : '暂停' }}
          </t-button>
          <t-select v-model="logFilter" size="small" style="width: 120px;">
            <t-option value="all">全部</t-option>
            <t-option value="hardware">硬件</t-option>
            <t-option value="system">系统</t-option>
            <t-option value="error">错误</t-option>
          </t-select>
        </div>
      </div>
      
      <div class="log-container" ref="logContainer">
        <div 
          v-for="log in filteredLogs" 
          :key="log.id"
          :class="['log-item', log.category, log.level]"
        >
          <span class="timestamp">{{ formatTime(log.timestamp) }}</span>
          <span class="category">{{ log.category }}</span>
          <span class="method">{{ log.method }}</span>
          <span class="message">{{ log.message }}</span>
          <span class="duration" v-if="log.duration">{{ log.duration }}ms</span>
        </div>
      </div>
    </div>
    
    <!-- 监听器详情 -->
    <div class="listeners-section">
      <h3>活跃监听器</h3>
      <div class="listeners-list">
        <div 
          v-for="detail in listenerStats.details" 
          :key="detail.method"
          class="listener-item"
        >
          <span class="method">{{ detail.method }}</span>
          <span class="count">{{ detail.count }} 个监听器</span>
          <t-button size="small" variant="text" @click="removeAllListeners(detail.method)">
            移除全部
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onUnmounted } from 'vue'
import { native } from '@/bridge'
import { useSystemEvents } from '@/composables/useNativeDevice'
import { useNativeConfig, useNativePerformance } from '@/composables/useNativeConfig'

interface LogEntry {
  id: string
  timestamp: number
  method: string
  message: string
  category: 'hardware' | 'system' | 'business' | 'error' | 'rpc'
  level: 'info' | 'warn' | 'error'
  params?: any
  duration?: number
}

// 组合式函数
const systemEvents = useSystemEvents()
const { getListenerStats } = useNativeConfig()
const performance = useNativePerformance()

// 状态
const eventLog = ref<LogEntry[]>([])
const isPaused = ref(false)
const logFilter = ref('all')
const logContainer = ref<HTMLElement>()

// 计算属性
const successRate = computed(() => {
  const total = performance.metrics.value.totalCalls
  const errors = performance.metrics.value.totalErrors
  return total > 0 ? ((total - errors) / total * 100) : 100
})

const listenerStats = computed(() => getListenerStats())

const filteredLogs = computed(() => {
  if (logFilter.value === 'all') return eventLog.value
  return eventLog.value.filter(log => log.category === logFilter.value)
})

// 全局事件监听
const unsubscribe = native.on('', (params, { method }) => {
  if (isPaused.value) return
  
  addLog({
    method,
    category: getCategoryFromMethod(method),
    level: 'info',
    message: generateLogMessage(method, params),
    params
  })
}, { 
  context: { method: '' } // 接收所有事件
})

// 监听RPC调用（通过包装原始方法）
const originalExecuteMethod = native.executeMethod
if (originalExecuteMethod) {
  // 包装方法以监控性能
  native.executeMethod = performance.wrapMethodCall(originalExecuteMethod)
}

// 添加日志
const addLog = (log: Partial<LogEntry>) => {
  const logEntry: LogEntry = {
    id: Date.now().toString() + Math.random(),
    timestamp: Date.now(),
    category: 'rpc',
    level: 'info',
    message: '',
    ...log
  } as LogEntry
  
  eventLog.value.unshift(logEntry)
  
  // 限制日志数量
  if (eventLog.value.length > 1000) {
    eventLog.value = eventLog.value.slice(0, 1000)
  }
  
  // 自动滚动到顶部
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = 0
    }
  })
}

// 辅助函数
const getCategoryFromMethod = (method: string): LogEntry['category'] => {
  if (method.startsWith('hardware.')) return 'hardware'
  if (method.startsWith('system.')) return 'system'
  if (method.startsWith('error.')) return 'error'
  return 'rpc'
}

const generateLogMessage = (method: string, params: any): string => {
  switch (method) {
    case 'hardware.scanner.barcodeScanned':
      return `扫码: ${params?.barcode} (${params?.deviceName})`
    case 'hardware.printer.statusChanged':
      return `打印机: ${params?.isOnline ? '在线' : '离线'} - ${params?.paperStatus}`
    case 'hardware.printer.jobCompleted':
      return `打印完成: ${params?.orderId}`
    case 'hardware.printer.jobFailed':
      return `打印失败: ${params?.orderId} - ${params?.error}`
    case 'system.network.statusChanged':
      return `网络: ${params?.isOnline ? '已连接' : '已断开'} (${params?.connectionType})`
    case 'error.occurred':
      return `错误: [${params?.source}] ${params?.error}`
    default:
      return `${method}: ${JSON.stringify(params)?.substring(0, 100)}...`
  }
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}

// 操作函数
const clearLogs = () => {
  eventLog.value = []
  performance.resetMetrics()
}

const pauseLogging = () => {
  isPaused.value = !isPaused.value
}

const removeAllListeners = (method: string) => {
  native.off(method)
}

// 组件卸载清理
onUnmounted(() => {
  unsubscribe()
})
</script>

<style scoped>
.system-monitor {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.metrics-section {
  margin-bottom: 30px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.metric-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.metric-card h4 {
  margin: 0 0 12px 0;
  color: #333;
}

.metric-card p {
  margin: 4px 0;
  font-size: 14px;
}

.log-section {
  margin-bottom: 30px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.log-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.log-container {
  background: #1e1e1e;
  color: #fff;
  padding: 16px;
  border-radius: 8px;
  height: 400px;
  overflow-y: auto;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #333;
}

.log-item.hardware { border-left: 3px solid #52c41a; }
.log-item.system { border-left: 3px solid #1890ff; }
.log-item.error { border-left: 3px solid #ff4d4f; }
.log-item.rpc { border-left: 3px solid #722ed1; }

.timestamp { color: #888; width: 80px; flex-shrink: 0; }
.category { color: #faad14; width: 60px; flex-shrink: 0; }
.method { color: #1890ff; width: 200px; flex-shrink: 0; }
.message { flex: 1; }
.duration { color: #52c41a; width: 50px; flex-shrink: 0; text-align: right; }

.listeners-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e6e6e6;
}

.listener-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.listener-item:last-child {
  border-bottom: none;
}
</style>
```

### 🔧 依赖注入配置

```csharp
// Program.cs 或 App.xaml.cs 中的服务注册
public void ConfigureServices(IServiceCollection services)
{
    // 注册通知服务
    services.AddSingleton<INotificationService, NotificationService>();
    
    // 注册硬件通知服务
    services.AddSingleton<ScannerNotificationService>();
    services.AddSingleton<PrinterNotificationService>();
    services.AddSingleton<CashDrawerNotificationService>();
    
    // 注册系统通知服务
    services.AddSingleton<SystemNotificationService>();
    
    // 其他服务注册...
}
```

### ✅ 通知系统优势

1. **统一格式**: 所有通知都采用JSON-RPC 2.0标准格式
2. **类型安全**: TypeScript提供完整的类型检查
3. **易于维护**: 分层封装，职责清晰
4. **高性能**: 基于事件驱动，响应迅速
5. **可扩展**: 支持新增通知类型和处理器
6. **向后兼容**: 同时支持新的订阅方式和传统的DOM事件
7. **错误处理**: 完善的错误捕获和日志记录
8. **自动清理**: 组件卸载时自动取消订阅

这套完整的通知系统确保了C#与Vue之间的通信既标准化又高效，为POS系统提供了可靠的实时通信基础。 
这套完整的通知系统确保了C#与Vue之间的通信既标准化又高效，为POS系统提供了可靠的实时通信基础。 