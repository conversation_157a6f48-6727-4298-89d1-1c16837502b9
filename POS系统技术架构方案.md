# 跨平台POS收银系统技术架构方案

## 1. 用户需求分析和整理

### 1.1 功能性需求

#### 核心业务功能
- **商品管理**：商品录入、分类、库存管理
- **收银功能**：扫码收银、手动输入、优惠计算
- **支付集成**：支持多种支付方式（现金、支付宝、微信、银行卡）
- **会员管理**：会员注册、积分管理、等级管理
- **报表统计**：销售报表、库存报表、财务报表
- **订单管理**：订单查询、退款处理、交易记录

#### 硬件集成需求
- **扫码枪集成**：支持一维码、二维码扫描
- **打印机集成**：小票打印、标签打印
- **钱箱控制**：自动开启钱箱
- **键盘支持**：专用POS键盘、数字键盘
- **显示器支持**：客显、双屏显示

### 1.2 非功能性需求

#### 性能要求
- **响应时间**：扫码响应 ≤ 200ms，界面操作响应 ≤ 100ms
- **并发处理**：支持同时处理多个订单
- **内存占用**：总内存使用 ≤ 512MB
- **启动时间**：应用启动时间 ≤ 3秒

#### 可用性要求
- **离线能力**：网络断开时基本功能可用
- **稳定性**：连续运行24小时无崩溃
- **容错性**：硬件故障时优雅降级

#### 安全性要求
- **数据加密**：敏感数据传输和存储加密
- **访问控制**：用户权限管理
- **审计日志**：完整的操作记录

## 2. 技术选型说明

### 2.1 整体架构选型

**混合架构：C# WPF + WebView2 + Vue3**

**选型理由：**
1. **性能优势**：C#处理硬件集成和本地计算，Vue3处理UI交互
2. **开发效率**：Vue3生态丰富，UI开发快速
3. **维护性**：前后端分离，职责清晰
4. **扩展性**：Web端可复用到其他平台

### 2.2 前端技术栈

#### Vue3 + TypeScript
```typescript
// 选择TypeScript的理由
// 1. 类型安全，减少运行时错误
// 2. 更好的IDE支持和代码提示
// 3. 大型项目的可维护性
// 4. 与C#端接口类型定义统一
```

**核心技术栈：**
- **Vue 3.4+**：组合式API，更好的性能
- **TypeScript 5.0+**：类型安全，开发体验
- **TDesign Vue Next**：企业级UI组件库
- **Pinia**：状态管理，替代Vuex
- **Vue Router 4**：路由管理
- **Axios**：HTTP客户端
- **VueUse**：组合式工具库

**构建工具：**
- **Vite 5.0+**：快速构建和热更新
- **ESLint + Prettier**：代码规范
- **Husky + lint-staged**：Git钩子

### 2.3 后端技术栈

#### C# .NET 8
```csharp
// 选择.NET 8的理由
// 1. 最新LTS版本，长期支持
// 2. 性能优化和AOT支持
// 3. 丰富的硬件集成库
// 4. 优秀的WebView2集成
```

**核心技术栈：**
- **.NET 8**：主要运行时
- **WPF**：桌面应用框架
- **WebView2**：Web内容嵌入
- **Entity Framework Core**：本地缓存数据访问

- **HttpClient**：Java中台API调用
- **Serilog**：日志框架
- **AutoMapper**：对象映射

## 3. 系统整体架构设计

### 3.1 架构层次图

```
┌─────────────────────────────────────────────────────────┐
│                    客户端层                              │
├─────────────────┬─────────────────┬─────────────────────┤
│  Vue3 前端界面   │  WebView2 容器   │   C# WPF 主程序      │
└─────────────────┴─────────────────┴─────────────────────┘
                           │
┌─────────────────────────────────────────────────────────┐
│                    通信层                                │
├─────────────────┬─────────────────┬─────────────────────┤
│ JavaScript Bridge│ C# WebView2 API │     事件总线         │
└─────────────────┴─────────────────┴─────────────────────┘
                           │
┌─────────────────────────────────────────────────────────┐
│                  业务逻辑层                              │
├─────────────────┬─────────────────┬─────────────────────┤
│   收银业务模块   │   商品管理模块   │   会员管理模块       │
└─────────────────┴─────────────────┴─────────────────────┘
                           │
┌─────────────────────────────────────────────────────────┐
│                 硬件集成层                               │
├─────────────────┬─────────────────┬─────────────────────┤
│   扫码枪驱动     │   打印机驱动     │    钱箱控制器        │
└─────────────────┴─────────────────┴─────────────────────┘
                           │
┌─────────────────────────────────────────────────────────┐
│                 数据访问层                               │
├─────────────────┬─────────────────┬─────────────────────┤
│   HTTP 客户端   │   JSON-RPC      │     系统配置         │
└─────────────────┴─────────────────┴─────────────────────┘
                           │
┌─────────────────────────────────────────────────────────┐
│                  外部服务                                │
├─────────────────┬─────────────────┬─────────────────────┤
│   支付服务API    │  云端数据同步    │     更新服务         │
└─────────────────┴─────────────────┴─────────────────────┘
```

### 3.2 数据流向设计

```
Vue3前端 ── JSON-RPC ──→ C#桥接层 ──→ 硬件设备
    ↓                        ↑
HTTP API调用           JSON-RPC通知
    ↓                        ↑
Java中台系统 ←──────────────┘
    (业务数据处理)
```

1. **硬件操作流**：Vue3 UI → JSON-RPC请求 → C# 硬件调用 → 设备操作
2. **硬件事件流**：硬件设备 → C# 驱动 → JSON-RPC通知 → Vue3 UI更新
3. **业务数据流**：Vue3 → HTTP API → Java中台 → 业务处理

## 4. 通信协议设计

### 4.1 C# 与 Vue 双向通信协议

#### 4.1.1 通信方式选择
- **C# → Vue**：使用 `webView.CoreWebView2.PostWebMessageAsJson()`
- **Vue → C#**：使用 `window.chrome.webview.postMessage()`

#### 4.1.2 JSON-RPC 2.0 协议格式

```typescript
// JSON-RPC 2.0 请求格式
interface JsonRpcRequest {
  jsonrpc: '2.0';
  id: string;           // 请求唯一标识
  method: string;       // 方法名称 (如: printer.printTicket)
  params?: any;         // 方法参数
}

// JSON-RPC 2.0 响应格式
interface JsonRpcResponse {
  jsonrpc: '2.0';
  id: string;           // 对应请求ID
  result?: any;         // 成功结果
  error?: {             // 错误信息
    code: number;
    message: string;
    data?: any;
  };
}

// JSON-RPC 2.0 通知格式 (C# -> Vue)
interface JsonRpcNotification {
  jsonrpc: '2.0';
  method: string;       // 事件名称 (如: hardware.scannerEvent)
  params?: any;         // 事件参数
}

// 错误代码定义
enum JsonRpcErrorCode {
  PARSE_ERROR = -32700,
  INVALID_REQUEST = -32600,
  METHOD_NOT_FOUND = -32601,
  INVALID_PARAMS = -32602,
  INTERNAL_ERROR = -32603,
  HARDWARE_ERROR = -32000,    // 自定义：硬件错误
  NETWORK_ERROR = -32001,     // 自定义：网络错误
  CACHE_ERROR = -32002        // 自定义：缓存错误
}
```

#### 4.1.3 JSON-RPC 2.0 统一桥接实现

**TypeScript端 - 统一动态代理客户端：**

```typescript
// src/bridge/index.ts
import { v4 as uuidv4 } from 'uuid';

interface PendingPromise {
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
  timeoutTimer: number;
}

interface ListenerConfig {
  handler: (...args: any[]) => void | Promise<void>;
  once?: boolean;
  context?: any;
}

interface MethodConfig {
  requiresReturn?: boolean;
  timeout?: number;
  retries?: number;
}

class UnifiedJsonRpcClient {
  private pendingPromises = new Map<string, PendingPromise>();
  private listeners = new Map<string, Set<ListenerConfig>>();
  private methodConfigs = new Map<string, MethodConfig>();
  private readonly DEFAULT_TIMEOUT = 30000;
  
  constructor() {
    this.initializeMessageListener();
  }
  
  private initializeMessageListener() {
    window.chrome?.webview?.addEventListener('message', (event) => {
      try {
        const message = JSON.parse(event.data);
        
        // 处理RPC响应
        if (message.id && this.pendingPromises.has(message.id)) {
          this.handleRpcResponse(message);
          return;
        }
        
        // 处理通知事件
        if (!message.id && message.method) {
          this.handleNotification(message);
          return;
        }
        
      } catch (error) {
        console.error('Failed to process message from native:', error);
      }
    });
  }
  
  private handleRpcResponse(response: any) {
    const promise = this.pendingPromises.get(response.id)!;
    window.clearTimeout(promise.timeoutTimer);
    
    if (response.error) {
      promise.reject(new Error(`[RPC Error ${response.error.code}] ${response.error.message}`));
    } else {
      promise.resolve(response.result);
    }
    
    this.pendingPromises.delete(response.id);
  }
  
  private async handleNotification(notification: any) {
    const { method, params } = notification;
    const methodListeners = this.listeners.get(method);
    
    if (methodListeners && methodListeners.size > 0) {
      // 使用Set的副本避免在遍历时修改
      const listenersArray = Array.from(methodListeners);
      
      for (const config of listenersArray) {
        try {
          // 执行监听器
          const result = config.handler.call(config.context || null, params);
          
          // 如果是Promise，等待完成
          if (result instanceof Promise) {
            await result;
          }
          
          // 如果是一次性监听器，移除它
          if (config.once) {
            methodListeners.delete(config);
          }
          
        } catch (error) {
          console.error(`Listener error for ${method}:`, error);
        }
      }
    }
    
    // 向后兼容：触发DOM事件
    document.dispatchEvent(new CustomEvent(method, { detail: params }));
    
    console.log(`🔔 Notification [${method}]:`, params);
  }
  
  /**
   * 配置方法行为
   */
  configureMethod(method: string, config: MethodConfig) {
    this.methodConfigs.set(method, config);
  }
  
  /**
   * 执行RPC方法调用
   * @param method 方法名
   * @param params 方法参数
   * @param callConfig 单次调用配置（优先级高于全局配置）
   */
  private async executeMethod(method: string, params: any[], callConfig?: MethodConfig): Promise<any> {
    // 合并全局配置和单次配置（单次配置优先）
    const globalConfig = this.methodConfigs.get(method) || {};
    const config = { ...globalConfig, ...callConfig };
    
    const timeout = config.timeout || this.DEFAULT_TIMEOUT;
    
    // 如果配置为不需要返回值，直接发送通知格式
    if (config.requiresReturn === false) {
      const notification = {
        jsonrpc: '2.0',
        method,
        params: params.length === 1 ? params[0] : params,
      };
      
      window.chrome?.webview?.postMessage(JSON.stringify(notification));
      
      // 记录单次调用（用于调试）
      if (callConfig) {
        console.log(`🔥 Fire-and-forget call [${method}] with config:`, callConfig);
      }
      
      return Promise.resolve(undefined);
    }
    
    // 正常的RPC调用
    return new Promise((resolve, reject) => {
      const id = uuidv4();
      
      const timeoutTimer = window.setTimeout(() => {
        this.pendingPromises.delete(id);
        reject(new Error(`RPC call timeout for method: ${method} (timeout: ${timeout}ms)`));
      }, timeout);
      
      this.pendingPromises.set(id, { resolve, reject, timeoutTimer });
      
      const request = {
        jsonrpc: '2.0',
        id,
        method,
        params: params.length === 1 ? params[0] : params,
      };
      
      // 记录单次调用配置
      if (callConfig) {
        console.log(`📞 RPC call [${method}] with config:`, callConfig);
      }
      
      window.chrome?.webview?.postMessage(JSON.stringify(request));
    });
  }
  
  /**
   * 添加事件监听器
   */
  private addListener(method: string, handler: (...args: any[]) => void | Promise<void>, options: { once?: boolean; context?: any } = {}) {
    if (!this.listeners.has(method)) {
      this.listeners.set(method, new Set());
    }
    
    const config: ListenerConfig = {
      handler,
      once: options.once,
      context: options.context
    };
    
    this.listeners.get(method)!.add(config);
    
    // 返回取消监听的函数
    return () => {
      const methodListeners = this.listeners.get(method);
      if (methodListeners) {
        methodListeners.delete(config);
        if (methodListeners.size === 0) {
          this.listeners.delete(method);
        }
      }
    };
  }
  
  /**
   * 移除事件监听器
   */
  private removeListener(method: string, handler?: (...args: any[]) => void | Promise<void>) {
    const methodListeners = this.listeners.get(method);
    if (!methodListeners) return;
    
    if (!handler) {
      // 移除所有监听器
      this.listeners.delete(method);
    } else {
      // 移除特定监听器
      for (const config of methodListeners) {
        if (config.handler === handler) {
          methodListeners.delete(config);
          break;
        }
      }
      
      if (methodListeners.size === 0) {
        this.listeners.delete(method);
      }
    }
  }
  
  /**
   * 创建动态代理
   */
  createProxy(path: string[] = []): any {
    return new Proxy(() => {}, {
      get: (target, prop: string) => {
        // 避免Promise then调用
        if (prop === 'then') return undefined;
        
        // 特殊方法处理
        switch (prop) {
          case 'on':
            return (method: string, handler: (...args: any[]) => void | Promise<void>, options?: { once?: boolean; context?: any }) => {
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              return this.addListener(fullMethod, handler, options);
            };
            
          case 'once':
            return (method: string, handler: (...args: any[]) => void | Promise<void>, context?: any) => {
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              return this.addListener(fullMethod, handler, { once: true, context });
            };
            
          case 'off':
            return (method: string, handler?: (...args: any[]) => void | Promise<void>) => {
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              this.removeListener(fullMethod, handler);
            };
            
          case 'configure':
            return (method: string, config: MethodConfig) => {
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              this.configureMethod(fullMethod, config);
              return this.createProxy(path); // 返回代理以支持链式调用
            };
            
          case 'listeners':
            return (method?: string) => {
              if (!method) {
                return Array.from(this.listeners.keys());
              }
              const fullMethod = path.length > 0 ? `${path.join('.')}.${method}` : method;
              const methodListeners = this.listeners.get(fullMethod);
              return methodListeners ? methodListeners.size : 0;
            };
            
          default:
            return this.createProxy([...path, prop]);
        }
      },
      
      apply: (target, thisArg, args) => {
        const method = path.join('.');
        
        // 检查最后一个参数是否为配置对象
        let params = args;
        let callConfig: MethodConfig | undefined;
        
        if (args.length > 0) {
          const lastArg = args[args.length - 1];
          
          // 如果最后一个参数是配置对象（包含特定的配置字段）
          if (lastArg && typeof lastArg === 'object' && 
              (lastArg.hasOwnProperty('requiresReturn') || 
               lastArg.hasOwnProperty('timeout') || 
               lastArg.hasOwnProperty('retries'))) {
            callConfig = lastArg;
            params = args.slice(0, -1); // 移除配置参数
          }
        }
        
        return this.executeMethod(method, params, callConfig);
      },
    });
  }
}

// 创建全局实例
const rpcClient = new UnifiedJsonRpcClient();

// 导出统一的native接口
export const native = rpcClient.createProxy();

// 导出类型定义
export interface NativeApi {
  // 硬件控制方法
  printer: {
    printTicket(data: PrintTicketData): Promise<PrintResult>;
    getStatus(): Promise<PrinterStatus>;
    cancelJob(jobId: string): Promise<boolean>;
    
    // 事件监听
    on(event: 'statusChanged', handler: (params: PrinterStatusParams) => void): () => void;
    on(event: 'jobCompleted', handler: (params: PrintJobParams) => void): () => void;
    on(event: 'jobFailed', handler: (params: PrintJobParams) => void): () => void;
    once(event: string, handler: (...args: any[]) => void): () => void;
    off(event: string, handler?: (...args: any[]) => void): void;
  };
  
  scanner: {
    getStatus(): Promise<ScannerStatus>;
    startScan(): Promise<void>;
    stopScan(): Promise<void>;
    testConnection(): Promise<boolean>;
    
    // 事件监听
    on(event: 'barcodeScanned', handler: (params: ScannerEventParams) => void): () => void;
    on(event: 'error', handler: (params: ScannerErrorParams) => void): () => void;
    on(event: 'connectionChanged', handler: (params: ConnectionParams) => void): () => void;
    once(event: string, handler: (...args: any[]) => void): () => void;
    off(event: string, handler?: (...args: any[]) => void): void;
  };
  
  cashDrawer: {
    open(): Promise<boolean>;
    getStatus(): Promise<DrawerStatus>;
    
    // 事件监听
    on(event: 'opened', handler: (params: DrawerEventParams) => void): () => void;
    on(event: 'statusChanged', handler: (params: DrawerStatusParams) => void): () => void;
    on(event: 'error', handler: (params: DrawerErrorParams) => void): () => void;
    once(event: string, handler: (...args: any[]) => void): () => void;
    off(event: string, handler?: (...args: any[]) => void): void;
  };
  
  // 系统功能
  app: {
    getVersion(): Promise<string>;
    restart(): void;
    getSystemInfo(): Promise<SystemInfo>;
    
    // 事件监听
    on(event: 'updateAvailable', handler: (params: UpdateParams) => void): () => void;
    on(event: 'memoryWarning', handler: (params: MemoryParams) => void): () => void;
    once(event: string, handler: (...args: any[]) => void): () => void;
    off(event: string, handler?: (...args: any[]) => void): void;
  };
  
  // 系统级事件监听
  system: {
    on(event: 'networkStatusChanged', handler: (params: NetworkParams) => void): () => void;
    on(event: string, handler: (...args: any[]) => void): () => void;
    once(event: string, handler: (...args: any[]) => void): () => void;
    off(event: string, handler?: (...args: any[]) => void): void;
  };
  
  // 全局配置和监听
  configure(method: string, config: MethodConfig): NativeApi;
  on(method: string, handler: (...args: any[]) => void, options?: { once?: boolean; context?: any }): () => void;
  once(method: string, handler: (...args: any[]) => void, context?: any): () => void;
  off(method: string, handler?: (...args: any[]) => void): void;
  listeners(method?: string): string[] | number;
}

// 方法配置接口
export interface MethodConfig {
  requiresReturn?: boolean;  // 是否需要返回值，默认true
  timeout?: number;          // 超时时间，默认30000ms  
  retries?: number;          // 重试次数，默认0
}

// 事件参数类型
export interface PrinterStatusParams {
  isOnline: boolean;
  paperStatus: 'OK' | 'EMPTY' | 'LOW';
  errorMessage?: string;
  deviceName: string;
  timestamp: number;
}

export interface PrintJobParams {
  jobId?: string;
  orderId: string;
  success?: boolean;
  error?: string;
  errorCode?: string;
  deviceName: string;
  timestamp: number;
}

export interface ScannerEventParams {
  barcode: string;
  deviceName: string;
  barcodeType?: string;
  timestamp: number;
}

export interface ScannerErrorParams {
  barcode?: string;
  error: string;
  errorCode: string;
  deviceName: string;
  timestamp: number;
}

export interface ConnectionParams {
  isConnected: boolean;
  deviceName: string;
  timestamp: number;
}

export interface DrawerEventParams {
  success: boolean;
  deviceName: string;
  timestamp: number;
}

export interface DrawerStatusParams {
  isOpen: boolean;
  deviceName: string;
  timestamp: number;
}

export interface DrawerErrorParams {
  error: string;
  operation: string;
  deviceName: string;
  timestamp: number;
}

export interface UpdateParams {
  version: string;
  isForceUpdate: boolean;
  description: string;
  downloadUrl: string;
  timestamp: number;
}

export interface MemoryParams {
  usedMemory: number;
  totalMemory: number;
  usagePercentage: number;
  timestamp: number;
}

export interface NetworkParams {
  isOnline: boolean;
  connectionType?: 'WiFi' | 'Ethernet' | 'Mobile' | 'Unknown';
  timestamp: number;
}
```

**使用示例：**

```typescript
// 1. 方法调用（默认需要返回值）
const result = await native.printer.printTicket(ticketData);
const status = await native.scanner.getStatus();

// 2. 全局配置方法行为
native.configure('printer.beep', { requiresReturn: false });
await native.printer.beep(); // 不等待返回，立即resolve

// 3. 单次调用配置（优先级高于全局配置）
await native.printer.beep({}, { requiresReturn: false }); // 单次不需要返回值
await native.printer.printTicket(data, { timeout: 45000, retries: 2 }); // 单次自定义超时和重试

// 4. 混合使用全局和单次配置
native.configure('scanner.scan', { timeout: 5000 }); // 全局超时5秒
await native.scanner.scan(); // 使用全局配置：5秒超时
await native.scanner.scan({}, { timeout: 10000 }); // 单次配置：10秒超时

// 5. 事件监听 - 设备级监听
const unsubscribe = native.scanner.on('barcodeScanned', (params) => {
  console.log('扫码:', params.barcode);
});

// 6. 事件监听 - 全局监听
native.on('hardware.printer.statusChanged', (params) => {
  console.log('打印机状态:', params.isOnline);
});

// 7. 一次性监听
native.printer.once('jobCompleted', (params) => {
  console.log('打印完成:', params.orderId);
});

// 8. 移除监听
native.scanner.off('barcodeScanned');
unsubscribe(); // 或使用返回的取消函数

// 9. 链式配置
native
  .configure('printer.beep', { requiresReturn: false })
  .configure('scanner.beep', { requiresReturn: false, timeout: 1000 });

// 10. 查看监听器状态
const methods = native.listeners(); // 获取所有有监听器的方法
const count = native.listeners('hardware.scanner.barcodeScanned'); // 获取特定方法的监听器数量

// 11. 高级单次配置示例
await native.cashDrawer.open({}, { 
  requiresReturn: true,    // 需要返回值
  timeout: 8000,          // 超时8秒
  retries: 1              // 重试1次
});

// 12. 批量操作时的灵活配置
const items = [item1, item2, item3];
for (const item of items) {
  // 最后一个item需要确认返回值，前面的不需要
  const isLast = item === items[items.length - 1];
  await native.inventory.updateStock(item, { 
    requiresReturn: isLast,
    timeout: isLast ? 10000 : 3000
  });
}
```

**C#端 - JSON-RPC 2.0 处理器：**
```csharp
// JSON-RPC处理器
public class JsonRpcHandler
{
    private readonly WebView2 _webView;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<JsonRpcHandler> _logger;
    private readonly Dictionary<string, Func<object[], Task<object>>> _methods;
    
    public JsonRpcHandler(WebView2 webView, IServiceProvider serviceProvider, ILogger<JsonRpcHandler> logger)
    {
        _webView = webView;
        _serviceProvider = serviceProvider;
        _logger = logger;
        _methods = new Dictionary<string, Func<object[], Task<object>>>();
        
        RegisterMethods();
        _webView.CoreWebView2.WebMessageReceived += OnWebMessageReceived;
    }
    
    private void RegisterMethods()
    {
        // 打印机方法
        _methods["printer.printTicket"] = async (args) =>
        {
            var printService = _serviceProvider.GetRequiredService<IPrintService>();
            var data = JsonSerializer.Deserialize<PrintTicketRequest>(args[0].ToString()!);
            var jobId = await printService.PrintTicketAsync(data.OrderId, data.Content);
            return new { success = true, jobId };
        };
        
        _methods["printer.getStatus"] = async (args) =>
        {
            var printService = _serviceProvider.GetRequiredService<IPrintService>();
            var status = await printService.GetStatusAsync();
            return new { isOnline = status.IsOnline, paperStatus = status.PaperStatus };
        };
        
        // 扫码枪方法
        _methods["scanner.getStatus"] = async (args) =>
        {
            var scannerService = _serviceProvider.GetRequiredService<IScannerService>();
            var status = await scannerService.GetStatusAsync();
            return new { isConnected = status.IsConnected, deviceName = status.DeviceName };
        };
        
        // 钱箱方法
        _methods["cashDrawer.open"] = async (args) =>
        {
            var cashDrawerService = _serviceProvider.GetRequiredService<ICashDrawerService>();
            return await cashDrawerService.OpenAsync();
        };
        
        _methods["cashDrawer.getStatus"] = async (args) =>
        {
            var cashDrawerService = _serviceProvider.GetRequiredService<ICashDrawerService>();
            var status = await cashDrawerService.GetStatusAsync();
            return new { isOpen = status.IsOpen, isConnected = status.IsConnected };
        };
        
        // 应用方法
        _methods["app.getVersion"] = async (args) =>
        {
            return Assembly.GetExecutingAssembly().GetName().Version?.ToString();
        };
        
        _methods["app.restart"] = async (args) =>
        {
            // 重启应用
            Process.Start(Environment.ProcessPath ?? Application.ExecutablePath);
            Application.Current.Shutdown();
            return null;
        };
    }
    
    private async void OnWebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
    {
        try
        {
            var requestJson = e.TryGetWebMessageAsString();
            var request = JsonSerializer.Deserialize<JsonRpcRequest>(requestJson);
            
            var response = await ProcessRequest(request);
            var responseJson = JsonSerializer.Serialize(response);
            
            await _webView.CoreWebView2.PostWebMessageAsStringAsync(responseJson);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "JSON-RPC请求处理失败");
        }
    }
    
    private async Task<JsonRpcResponse> ProcessRequest(JsonRpcRequest request)
    {
        var response = new JsonRpcResponse
        {
            JsonRpc = "2.0",
            Id = request.Id
        };
        
        try
        {
            if (!_methods.ContainsKey(request.Method))
            {
                response.Error = new JsonRpcError
                {
                    Code = (int)JsonRpcErrorCode.METHOD_NOT_FOUND,
                    Message = $"Method '{request.Method}' not found"
                };
                return response;
            }
            
            var method = _methods[request.Method];
            var parameters = request.Params as object[] ?? Array.Empty<object>();
            
            _logger.LogDebug("执行方法: {Method}, 参数: {Params}", request.Method, request.Params);
            
            response.Result = await method(parameters);
        }
        catch (ArgumentException ex)
        {
            response.Error = new JsonRpcError
            {
                Code = (int)JsonRpcErrorCode.INVALID_PARAMS,
                Message = ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "方法执行失败: {Method}", request.Method);
            response.Error = new JsonRpcError
            {
                Code = (int)JsonRpcErrorCode.INTERNAL_ERROR,
                Message = ex.Message
            };
        }
        
        return response;
    }
    
    // 发送通知到前端
    public async Task SendNotificationAsync(string method, object parameters = null)
    {
        var notification = new JsonRpcNotification
        {
            JsonRpc = "2.0",
            Method = method,
            Params = parameters
        };
        
        var json = JsonSerializer.Serialize(notification);
        await _webView.CoreWebView2.PostWebMessageAsStringAsync(json);
    }
}

// JSON-RPC数据模型
public class JsonRpcRequest
{
    [JsonPropertyName("jsonrpc")]
    public string JsonRpc { get; set; } = "2.0";
    
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("method")]
    public string Method { get; set; }
    
    [JsonPropertyName("params")]
    public object Params { get; set; }
}

public class JsonRpcResponse
{
    [JsonPropertyName("jsonrpc")]
    public string JsonRpc { get; set; } = "2.0";
    
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("result")]
    public object Result { get; set; }
    
    [JsonPropertyName("error")]
    public JsonRpcError Error { get; set; }
}

public class JsonRpcNotification
{
    [JsonPropertyName("jsonrpc")]
    public string JsonRpc { get; set; } = "2.0";
    
    [JsonPropertyName("method")]
    public string Method { get; set; }
    
    [JsonPropertyName("params")]
    public object Params { get; set; }
}

public class JsonRpcError
{
    [JsonPropertyName("code")]
    public int Code { get; set; }
    
    [JsonPropertyName("message")]
    public string Message { get; set; }
    
    [JsonPropertyName("data")]
    public object Data { get; set; }
}

public class PrintTicketRequest
{
    public string OrderId { get; set; }
    public string Content { get; set; }
}
```

### 4.2 硬件事件处理

```csharp
// 扫码枪事件处理
public class ScannerService : IScannerService
{
    private readonly JsonRpcHandler _rpcHandler;
    private readonly ILogger<ScannerService> _logger;
    
    public event EventHandler<string> BarcodeScanned;
    
    public ScannerService(
        JsonRpcHandler rpcHandler, 
        ILogger<ScannerService> logger)
    {
        _rpcHandler = rpcHandler;
        _logger = logger;
        InitializeScanner();
    }
    
    private async void OnBarcodeScanned(string barcode)
    {
        try
        {
            _logger.LogInformation("扫码成功: {Barcode}", barcode);
            
            // 发送JSON-RPC通知到Vue端，由Vue端处理商品查询
            await _rpcHandler.SendNotificationAsync("hardware.barcodeScanned", new 
            { 
                barcode = barcode,
                deviceName = GetDeviceName(),
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            });
            
            // 触发本地事件
            BarcodeScanned?.Invoke(this, barcode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扫码处理失败: {Barcode}", barcode);
            
            // 发送错误通知
            await _rpcHandler.SendNotificationAsync("hardware.scannerError", new 
            { 
                barcode = barcode,
                error = ex.Message,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            });
        }
    }
    
    public async Task<ScannerStatus> GetStatusAsync()
    {
        return new ScannerStatus
        {
            IsConnected = IsDeviceConnected(),
            DeviceName = GetDeviceName(),
            LastScanTime = _lastScanTime
        };
    }
}
```

#### 4.2.2 打印机事件处理

```csharp
public class PrinterService : IPrinterService
{
    private readonly JsonRpcHandler _rpcHandler;
    private readonly ILogger<PrinterService> _logger;
    
    public PrinterService(JsonRpcHandler rpcHandler, ILogger<PrinterService> logger)
    {
        _rpcHandler = rpcHandler;
        _logger = logger;
        MonitorPrinterStatus();
    }
    
    // 监控打印机状态变化
    private async void MonitorPrinterStatus()
    {
        while (true)
        {
            try
            {
                var currentStatus = await GetStatusAsync();
                
                // ✅ C# -> Vue 通知：打印机状态变化
                await _rpcHandler.SendNotificationAsync("hardware.printerStatusChanged", new 
                { 
                    isOnline = currentStatus.IsOnline,
                    paperStatus = currentStatus.PaperStatus,
                    errorMessage = currentStatus.ErrorMessage,
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                });
                
                await Task.Delay(5000); // 每5秒检查一次
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打印机状态监控异常");
                await Task.Delay(10000);
            }
        }
    }
    
    public async Task<PrintResult> PrintTicketAsync(PrintTicketData data)
    {
        try
        {
            // 打印逻辑...
            var result = new PrintResult
            {
                Success = true,
                JobId = Guid.NewGuid().ToString()
            };
            
            // ✅ C# -> Vue 通知：打印任务完成
            await _rpcHandler.SendNotificationAsync("hardware.printJobCompleted", new 
            { 
                jobId = result.JobId,
                orderId = data.OrderId,
                success = true,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            });
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打印失败: {OrderId}", data.OrderId);
            
            // ✅ C# -> Vue 通知：打印任务失败
            await _rpcHandler.SendNotificationAsync("hardware.printJobFailed", new 
            { 
                orderId = data.OrderId,
                error = ex.Message,
                errorCode = "PRINT_FAILED",
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            });
            
            return new PrintResult { Success = false, Error = ex.Message };
        }
    }
}
```

#### 4.2.3 钱箱事件处理

```csharp
public class CashDrawerService : ICashDrawerService
{
    private readonly JsonRpcHandler _rpcHandler;
    private readonly ILogger<CashDrawerService> _logger;
    
    public CashDrawerService(JsonRpcHandler rpcHandler, ILogger<CashDrawerService> logger)
    {
        _rpcHandler = rpcHandler;
        _logger = logger;
        MonitorDrawerStatus();
    }
    
    public async Task<bool> OpenAsync()
    {
        try
        {
            // 执行开钱箱操作...
            bool success = await ExecuteOpenCommand();
            
            if (success)
            {
                // ✅ C# -> Vue 通知：钱箱打开成功
                await _rpcHandler.SendNotificationAsync("hardware.cashDrawerOpened", new 
                { 
                    success = true,
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                });
            }
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "钱箱打开失败");
            
            // ✅ C# -> Vue 通知：钱箱操作失败
            await _rpcHandler.SendNotificationAsync("hardware.cashDrawerError", new 
            { 
                error = ex.Message,
                operation = "open",
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            });
            
            return false;
        }
    }
    
    // 监控钱箱状态（开关检测）
    private async void MonitorDrawerStatus()
    {
        bool lastStatus = false;
        
        while (true)
        {
            try
            {
                bool currentStatus = IsDrawerOpen();
                
                if (currentStatus != lastStatus)
                {
                    // ✅ C# -> Vue 通知：钱箱状态变化
                    await _rpcHandler.SendNotificationAsync("hardware.cashDrawerStatusChanged", new 
                    { 
                        isOpen = currentStatus,
                        timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                    });
                    
                    lastStatus = currentStatus;
                }
                
                await Task.Delay(1000); // 每秒检查一次
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "钱箱状态监控异常");
                await Task.Delay(5000);
            }
        }
    }
}
```

#### 4.2.4 C# -> Vue 通知事件汇总

| 事件名称 | 触发时机 | 参数说明 |
|---------|----------|----------|
| `hardware.barcodeScanned` | 扫码成功 | `{ barcode, deviceName, timestamp }` |
| `hardware.scannerError` | 扫码失败 | `{ barcode, error, errorCode, timestamp }` |
| `hardware.scannerConnectionChanged` | 扫码枪连接状态变化 | `{ isConnected, deviceName, timestamp }` |
| `hardware.printerStatusChanged` | 打印机状态变化 | `{ isOnline, paperStatus, errorMessage, timestamp }` |
| `hardware.printJobCompleted` | 打印任务完成 | `{ jobId, orderId, success, timestamp }` |
| `hardware.printJobFailed` | 打印任务失败 | `{ orderId, error, errorCode, timestamp }` |
| `hardware.cashDrawerOpened` | 钱箱打开成功 | `{ success, timestamp }` |
| `hardware.cashDrawerError` | 钱箱操作失败 | `{ error, operation, timestamp }` |
| `hardware.cashDrawerStatusChanged` | 钱箱状态变化 | `{ isOpen, timestamp }` |

### 4.3 Vue端使用示例

#### 4.3.1 完整的收银页面示例

```vue
<!-- CashierView.vue -->
<template>
  <div class="cashier-view">
    <div class="toolbar">
      <t-input 
        v-model="barcodeInput" 
        placeholder="扫码或手动输入商品条码"
        @enter="handleBarcodeInput"
        :loading="isSearching"
      />
      <t-button @click="handlePrintTest" :loading="isPrinting">
        测试打印
      </t-button>
      <t-button @click="handleCheckout" :disabled="cartItems.length === 0">
        结账 ({{cartItems.length}}件)
      </t-button>
    </div>
    
    <div class="main-content">
      <div class="cart-section">
        <h3>购物车</h3>
        <div v-for="item in cartItems" :key="item.id" class="cart-item">
          <span>{{item.name}} x{{item.quantity}}</span>
          <span>¥{{item.total.toFixed(2)}}</span>
        </div>
        <div class="total">总计: ¥{{totalAmount.toFixed(2)}}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { native } from '@/bridge'
import { productApi, orderApi } from '@/services/api'

// 响应式数据
const barcodeInput = ref('')
const cartItems = ref<CartItem[]>([])
const isSearching = ref(false)
const isPrinting = ref(false)

// 计算属性
const totalAmount = computed(() => 
  cartItems.value.reduce((sum, item) => sum + item.total, 0)
)

// 处理条码输入
const handleBarcodeInput = async () => {
  if (!barcodeInput.value.trim()) return
  
  isSearching.value = true
  try {
    // Vue直接调用HTTP API获取商品信息
    const product = await productApi.getByBarcode(barcodeInput.value)
    
    if (product) {
      addToCart(product)
      showMessage('商品已添加到购物车', 'success')
    } else {
      showMessage('商品不存在', 'warning')
    }
  } catch (error) {
    console.error('查询商品失败:', error)
    showMessage('网络错误，请重试', 'error')
  } finally {
    isSearching.value = false
    barcodeInput.value = ''
  }
}

// 添加到购物车
const addToCart = (product: Product) => {
  const existingItem = cartItems.value.find(item => item.productId === product.id)
  
  if (existingItem) {
    existingItem.quantity++
    existingItem.total = existingItem.quantity * existingItem.price
  } else {
    cartItems.value.push({
      id: generateId(),
      productId: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      total: product.price
    })
  }
}

// 完整的打印示例
const handlePrintTicket = async (orderData: OrderData) => {
  isPrinting.value = true
  try {
    // 使用JSON-RPC调用C#打印服务
    const printResult = await native.printer.printTicket({
      orderId: orderData.id,
      storeName: '测试商店',
      items: orderData.items.map(item => ({
        name: item.name,
        quantity: item.quantity,
        price: item.price,
        total: item.total
      })),
      totalAmount: orderData.totalAmount,
      paymentMethod: orderData.paymentMethod,
      cashReceived: orderData.cashReceived,
      change: orderData.change,
      timestamp: new Date().toLocaleString(),
      footer: '谢谢惠顾，欢迎下次光临！'
    })
    
    if (printResult.success) {
      console.log(`小票打印成功，任务ID: ${printResult.jobId}`)
      
      // 可选：打开钱箱
      await native.cashDrawer.open()
      
      return true
    } else {
      throw new Error(printResult.error || '打印失败')
    }
  } catch (error) {
    console.error('打印失败:', error)
    showMessage('打印失败：' + error.message, 'error')
    return false
  } finally {
    isPrinting.value = false
  }
}

// 结账流程
const handleCheckout = async () => {
  if (cartItems.value.length === 0) return
  
  try {
    // 1. Vue直接调用HTTP API创建订单
    const orderData = await orderApi.create({
      items: cartItems.value,
      totalAmount: totalAmount.value,
      paymentMethod: 'CASH',
      cashReceived: totalAmount.value,
      change: 0
    })
    
    // 2. 调用硬件打印小票
    const printSuccess = await handlePrintTicket(orderData)
    
    if (printSuccess) {
      // 3. 清空购物车
      cartItems.value = []
      showMessage('结账成功', 'success')
    }
    
  } catch (error) {
    console.error('结账失败:', error)
    showMessage('结账失败：' + error.message, 'error')
  }
}



// 测试打印功能
const handlePrintTest = async () => {
  await handlePrintTicket({
    id: 'TEST-' + Date.now(),
    items: [
      { name: '测试商品A', quantity: 1, price: 10.00, total: 10.00 },
      { name: '测试商品B', quantity: 2, price: 5.50, total: 11.00 }
    ],
    totalAmount: 21.00,
    paymentMethod: 'CASH',
    cashReceived: 25.00,
    change: 4.00
  })
}

// 监听硬件事件
onMounted(() => {
  // 监听扫码枪事件
  document.addEventListener('hardware.barcodeScanned', (event: any) => {
    const { barcode, product } = event.detail
    console.log('扫码事件:', barcode, product)
    
    if (product) {
      // 直接添加到购物车，因为已经包含商品信息
      addToCart(product)
    } else {
      // 显示商品不存在提示
      showMessage('商品不存在', 'warning')
    }
  })
  
  // 监听扫码枪错误
  document.addEventListener('hardware.scannerError', (event: any) => {
    const { error } = event.detail
    console.error('扫码枪错误:', error)
    showMessage('扫码枪发生错误', 'error')
  })
  
  // 监听网络状态变化
  document.addEventListener('system.networkStatusChanged', (event: any) => {
    const { isOnline } = event.detail
    console.log('网络状态:', isOnline ? '在线' : '离线')
    updateNetworkStatus(isOnline)
  })
})

// 应用启动时同步离线数据
// 监听硬件事件 
onMounted(() => {
  // 监听扫码事件
  document.addEventListener('hardware.barcodeScanned', async (event: any) => {
    const { barcode } = event.detail
    console.log('收到扫码:', barcode)
    
    // 自动处理扫码商品查询
    barcodeInput.value = barcode
    await handleBarcodeInput()
  })
  
  // 监听扫码错误事件
  document.addEventListener('hardware.scannerError', (event: any) => {
    const { error, errorCode } = event.detail
    showMessage(`扫码失败[${errorCode}]: ${error}`, 'error')
  })
  
  // 监听扫码枪连接状态变化
  document.addEventListener('hardware.scannerConnectionChanged', (event: any) => {
    const { isConnected, deviceName } = event.detail
    if (isConnected) {
      showMessage(`扫码枪已连接: ${deviceName}`, 'success')
    } else {
      showMessage(`扫码枪连接断开: ${deviceName}`, 'warning')
    }
  })
  
  // 监听打印机状态变化
  document.addEventListener('hardware.printerStatusChanged', (event: any) => {
    const { isOnline, paperStatus, errorMessage } = event.detail
    
    if (!isOnline) {
      showMessage('打印机离线', 'error')
    } else if (paperStatus === 'EMPTY') {
      showMessage('打印机缺纸，请添加纸张', 'warning')
    } else if (paperStatus === 'LOW') {
      showMessage('打印机纸张不足', 'warning')
    }
    
    if (errorMessage) {
      showMessage(`打印机错误: ${errorMessage}`, 'error')
    }
  })
  
  // 监听打印任务完成
  document.addEventListener('hardware.printJobCompleted', (event: any) => {
    const { orderId, jobId } = event.detail
    showMessage(`订单 ${orderId} 打印完成`, 'success')
  })
  
  // 监听打印任务失败
  document.addEventListener('hardware.printJobFailed', (event: any) => {
    const { orderId, error } = event.detail
    showMessage(`订单 ${orderId} 打印失败: ${error}`, 'error')
  })
  
  // 监听钱箱打开成功
  document.addEventListener('hardware.cashDrawerOpened', (event: any) => {
    console.log('钱箱已打开')
  })
  
  // 监听钱箱操作失败
  document.addEventListener('hardware.cashDrawerError', (event: any) => {
    const { error, operation } = event.detail
    showMessage(`钱箱${operation}失败: ${error}`, 'error')
  })
  
  // 监听钱箱状态变化
  document.addEventListener('hardware.cashDrawerStatusChanged', (event: any) => {
    const { isOpen } = event.detail
    console.log('钱箱状态:', isOpen ? '打开' : '关闭')
  })
})
</script>
```

#### 4.3.2 HTTP API服务封装

```typescript
// services/api/product.ts
import { httpClient } from './base'

export const productApi = {
  // 根据条码获取商品
  async getByBarcode(barcode: string): Promise<Product | null> {
    try {
      const response = await httpClient.get(`/api/products/barcode/${barcode}`)
      return response.data
    } catch (error) {
      if (error.response?.status === 404) {
        return null
      }
      throw error
    }
  },
  
  // 搜索商品
  async search(keyword: string, page = 1, size = 20): Promise<ProductSearchResult> {
    const response = await httpClient.get('/api/products/search', {
      params: { keyword, page, size }
    })
    return response.data
  },
  
  // 获取商品详情
  async getById(id: string): Promise<Product> {
    const response = await httpClient.get(`/api/products/${id}`)
    return response.data
  }
}

// services/api/order.ts
export const orderApi = {
  // 创建订单
  async create(orderData: CreateOrderRequest): Promise<Order> {
    const response = await httpClient.post('/api/orders', orderData)
    return response.data
  },
  
  // 获取订单历史
  async getHistory(params: OrderHistoryParams): Promise<OrderHistoryResult> {
    const response = await httpClient.get('/api/orders', { params })
    return response.data
  },
  
  // 退款
  async refund(orderId: string, reason: string): Promise<RefundResult> {
    const response = await httpClient.post(`/api/orders/${orderId}/refund`, { reason })
    return response.data
  }
}

// services/api/base.ts
import axios from 'axios'

export const httpClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
httpClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加设备ID
    config.headers['X-Device-ID'] = getDeviceId()
    
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
httpClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // token过期，跳转登录
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)
```

#### 4.3.3 类型定义

```typescript
// types/business.ts
export interface Product {
  id: string
  name: string
  barcode: string
  price: number
  cost: number
  stock: number
  category: string
  imageUrl?: string
  description?: string
}

export interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  quantity: number
  total: number
}

export interface OrderData {
  id: string
  items: CartItem[]
  totalAmount: number
  paymentMethod: string
  cashReceived: number
  change: number
  createTime?: string
}

export interface CreateOrderRequest {
  items: Array<{
    productId: string
    quantity: number
    price: number
  }>
  totalAmount: number
  paymentMethod: string
  cashReceived: number
  change: number
}

export interface OfflineOrder {
  items: CartItem[]
  totalAmount: number
  paymentMethod: string
  timestamp: string
}

// JSON-RPC硬件接口类型
export interface PrintTicketData {
  orderId: string
  storeName: string
  items: Array<{
    name: string
    quantity: number
    price: number
    total: number
  }>
  totalAmount: number
  paymentMethod: string
  cashReceived: number
  change: number
  timestamp: string
  footer: string
}

export interface PrintResult {
  success: boolean
  jobId: string
  error?: string
}

export interface ScannerStatus {
  isConnected: boolean
  deviceName: string
  lastScanTime?: string
}

export interface PrinterStatus {
  isOnline: boolean
  paperStatus: 'OK' | 'EMPTY' | 'LOW'
  errorMessage?: string
}

export interface DrawerStatus {
  isOpen: boolean
  isConnected: boolean
}

export interface SystemInfo {
  version: string
  os: string
  memory: string
  diskSpace: string
}

// 硬件事件类型定义（C# -> Vue 通知）
export interface HardwareEventMap {
  'hardware.barcodeScanned': {
    barcode: string
    deviceName: string
    timestamp: number
  }
  'hardware.scannerError': {
    barcode: string
    error: string
    errorCode: string
    timestamp: number
  }
  'hardware.scannerConnectionChanged': {
    isConnected: boolean
    deviceName: string
    timestamp: number
  }
  'hardware.printerStatusChanged': {
    isOnline: boolean
    paperStatus: 'OK' | 'EMPTY' | 'LOW'
    errorMessage?: string
    timestamp: number
  }
  'hardware.printJobCompleted': {
    jobId: string
    orderId: string
    success: boolean
    timestamp: number
  }
  'hardware.printJobFailed': {
    orderId: string
    error: string
    errorCode: string
    timestamp: number
  }
  'hardware.cashDrawerOpened': {
    success: boolean
    timestamp: number
  }
  'hardware.cashDrawerError': {
    error: string
    operation: string
    timestamp: number
  }
  'hardware.cashDrawerStatusChanged': {
    isOpen: boolean
    timestamp: number
  }
}

// 事件监听器类型定义
export type HardwareEventListener<K extends keyof HardwareEventMap> = (
  event: CustomEvent<HardwareEventMap[K]>
) => void

// 类型安全的事件监听函数
export function addHardwareEventListener<K extends keyof HardwareEventMap>(
  type: K,
  listener: HardwareEventListener<K>
): void {
  document.addEventListener(type, listener as EventListener)
}

export function removeHardwareEventListener<K extends keyof HardwareEventMap>(
  type: K,
  listener: HardwareEventListener<K>
): void {
  document.removeEventListener(type, listener as EventListener)
}
```

**组合式函数封装：**
```typescript
// composables/useNative.ts
import { ref } from 'vue'
import { native } from '@/bridge'

export function useScanner() {
  const isScanning = ref(false)
  const lastBarcode = ref('')
  
  const startScan = async () => {
    try {
      await native.scanner.startScan()
      isScanning.value = true
    } catch (error) {
      console.error('启动扫码失败:', error)
    }
  }
  
  const stopScan = async () => {
    try {
      await native.scanner.stopScan()
      isScanning.value = false
    } catch (error) {
      console.error('停止扫码失败:', error)
    }
  }
  
  const getStatus = async () => {
    try {
      return await native.scanner.getStatus()
    } catch (error) {
      console.error('获取扫码枪状态失败:', error)
      return null
    }
  }
  
  return {
    isScanning,
    lastBarcode,
    startScan,
    stopScan,
    getStatus
  }
}

export function usePrinter() {
  const isPrinting = ref(false)
  
  const printTicket = async (data: { orderId: string; content: string }) => {
    try {
      isPrinting.value = true
      const result = await native.printer.printTicket(data)
      return result
    } catch (error) {
      console.error('打印失败:', error)
      throw error
    } finally {
      isPrinting.value = false
    }
  }
  
  const getStatus = async () => {
    try {
      return await native.printer.getStatus()
    } catch (error) {
      console.error('获取打印机状态失败:', error)
      return null
    }
  }
  
  return {
    isPrinting,
    printTicket,
    getStatus
  }
}

export function useJavaApi() {
  const callApi = async (endpoint: string, data: any) => {
    try {
      return await native.java.callApi(endpoint, data)
    } catch (error) {
      console.error('Java API调用失败:', error)
      throw error
    }
  }
  
  return {
    callApi
  }
}
```

## 5. C#端项目结构和实现细节

### 5.1 项目目录结构

```
POSSystem.Desktop/
├── POSSystem.Core/                    # 核心业务逻辑
│   ├── Entities/                      # 实体模型
│   │   ├── Product.cs
│   │   ├── Order.cs
│   │   ├── Customer.cs
│   │   └── Payment.cs
│   ├── Services/                      # 业务服务
│   │   ├── Interfaces/
│   │   │   ├── IProductService.cs
│   │   │   ├── IOrderService.cs
│   │   │   └── IPaymentService.cs
│   │   ├── ProductService.cs
│   │   ├── OrderService.cs
│   │   └── PaymentService.cs
│   ├── Repositories/                  # 数据访问
│   │   ├── Interfaces/
│   │   │   ├── IProductRepository.cs
│   │   │   └── IOrderRepository.cs
│   │   ├── ProductRepository.cs
│   │   └── OrderRepository.cs
│   └── Models/                        # DTO模型
│       ├── ProductDto.cs
│       ├── OrderDto.cs
│       └── PaymentDto.cs
│
├── POSSystem.Infrastructure/          # 基础设施层
│   ├── Data/                         # 数据访问
│   │   ├── POSDbContext.cs
│   │   ├── Configurations/
│   │   └── Migrations/
│   ├── Hardware/                     # 硬件集成
│   │   ├── Interfaces/
│   │   │   ├── IScannerService.cs
│   │   │   ├── IPrintService.cs
│   │   │   └── ICashDrawerService.cs
│   │   ├── ScannerService.cs
│   │   ├── PrintService.cs
│   │   └── CashDrawerService.cs
│   ├── Communication/                # 通信模块
│   │   ├── MessageHandler.cs
│   │   ├── WebViewBridge.cs
│   │   └── MessageTypes.cs
│   ├── Logging/                      # 日志模块
│   │   ├── LoggingService.cs
│   │   └── LogUploadService.cs
│   └── Updates/                      # 更新模块
│       ├── UpdateService.cs
│       ├── VersionChecker.cs
│       └── PackageManager.cs
│
├── POSSystem.WPF/                    # WPF主程序
│   ├── MainWindow.xaml
│   ├── MainWindow.xaml.cs
│   ├── App.xaml
│   ├── App.xaml.cs
│   ├── Views/                        # 视图
│   │   ├── SplashScreen.xaml
│   │   └── SettingsWindow.xaml
│   ├── ViewModels/                   # 视图模型
│   │   ├── MainViewModel.cs
│   │   └── SettingsViewModel.cs
│   ├── Controls/                     # 自定义控件
│   │   └── WebViewControl.xaml
│   └── Resources/                    # 资源文件
│       ├── Styles/
│       └── Images/
│
└── POSSystem.Tests/                  # 测试项目
    ├── Unit/
    ├── Integration/
    └── Hardware/
```

### 5.2 核心组件配置

#### 5.2.1 依赖注入配置

```csharp
// App.xaml.cs
public partial class App : Application
{
    private ServiceProvider serviceProvider;
    private ILogger<App> logger;
    
    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);
        
        try
        {
            // 配置服务容器
            var services = new ServiceCollection();
            ConfigureServices(services);
            serviceProvider = services.BuildServiceProvider();
            
            // 初始化日志
            logger = serviceProvider.GetRequiredService<ILogger<App>>();
            logger.LogInformation("应用程序启动");
            
            // 初始化数据库
            await InitializeDatabase();
            
            // 启动主窗口
            var mainWindow = serviceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误", 
                MessageBoxButton.OK, MessageBoxImage.Error);
            Current.Shutdown();
        }
    }
    
    private void ConfigureServices(IServiceCollection services)
    {
        // 日志配置
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(new LoggerConfiguration()
                .WriteTo.File("logs/pos-.log", rollingInterval: RollingInterval.Day)
                .WriteTo.Console()
                .CreateLogger());
        });
        
        // 本地缓存数据库配置
        services.AddDbContext<CacheDbContext>(options =>
        {
            var dbPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "POSSystem", "cache.db");
            options.UseSqlite($"Data Source={dbPath}");
        });
        
        // 缓存和中台服务
        services.AddScoped<ILocalCacheService, LocalCacheService>();
        services.AddScoped<IJavaMidtierProxy, JavaMidtierProxy>();
        
        // HTTP客户端配置
        services.AddHttpClient<IJavaMidtierProxy>(client =>
        {
            client.BaseAddress = new Uri(configuration["JavaMidtier:BaseUrl"]);
            client.Timeout = TimeSpan.FromMilliseconds(configuration.GetValue<int>("JavaMidtier:Timeout"));
            client.DefaultRequestHeaders.Add("X-API-Key", configuration["JavaMidtier:ApiKey"]);
        });
        
        // 硬件服务
        services.AddSingleton<IScannerService, ScannerService>();
        services.AddSingleton<IPrintService, PrintService>();
        services.AddSingleton<ICashDrawerService, CashDrawerService>();
        
        // JSON-RPC通信服务
        services.AddSingleton<JsonRpcHandler>();
        
        // 更新服务
        services.AddSingleton<IUpdateService, UpdateService>();
        
        // 主窗口
        services.AddTransient<MainWindow>();
        
        // AutoMapper
        services.AddAutoMapper(typeof(MappingProfile));
    }
    
    private async Task InitializeDatabase()
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<CacheDbContext>();
        
        await context.Database.EnsureCreatedAsync();
        
        // 初始化缓存表结构，不需要种子数据
        // 缓存数据将从Java中台动态获取
        logger.LogInformation("本地缓存数据库初始化完成");
    }
}
``` 